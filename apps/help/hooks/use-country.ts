"use client"

import { useState, useEffect } from "react"
import { getCookie } from "cookies-next"

export function useCountry() {
  const [country, setCountry] = useState<string | null>( null )
  const [loading, setLoading] = useState( true )

  useEffect( () => {
    // Get the country from the cookie set by middleware
    const userCountry = getCookie( "user-country" ) as string | undefined
    setCountry( userCountry || null )
    setLoading( false )
  }, [])

  return { country, loading, isTurkey: country === "TR" }
}
