{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/209-qasid-online-payment"], "title": "Qasid Online Payment", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "You will either be re-directed or sent an email with a link to a payment...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    em: \"em\",\n    img: \"img\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    ul: \"ul\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"You will either be re-directed or sent an email with a link to a payment page.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: [\"Once on this page, please click \", _jsxDEV(_components.em, {\n          children: \"Add the Deposit and Pay\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 4,\n          columnNumber: 36\n        }, this), \" as shown below:\"]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 4,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-hkeifFXgOe.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"The page will be updated with the Amount on the right and a Credit Card information box as shown below:\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-3GE0zKTIPt.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 10,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Please complete all the required fields (marked with an Asterix) are accurately completed and click  \", _jsxDEV(_components.em, {\n        children: \"Pay Invoice\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 102\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"First name, Last name\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 14,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Country, Street address, Town/City, Postcode/Zip\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 15,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Email address\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 16,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Card number, Expiration date, Security Code\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 17,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 14,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: [\"You will see a processing dial on top of the credit card box. Please wait as this may take a few seconds depending on your internet connection. You may be directed to a page such as the one below if your credit card company requires \", _jsxDEV(_components.em, {\n          children: \"3D secure\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 19,\n          columnNumber: 237\n        }, this), \" check i.e. a code will be sent to your phone.\"]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 19,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 19,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-vKI43wFu1Y.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 21,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 21,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"After you have submitted the code (if required), you will be directed to a page that will show a summary of the completed transaction such as the one below. If there was an error, you will be directed to a similar page but with an error message. If the transaction completed successfully, you will also receive an email receipt.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 23,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 23,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-Rz4fRL1kgO.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 25,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 25,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"If you received an error, please contact our merchant shopper support at this \", _jsxDEV(_components.a, {\n        href: \"https://www.2co.com\",\n        children: \"link\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 27,\n        columnNumber: 79\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 27,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/209-qasid-online-payment"], "title": "Qasid Online Payment", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "You will either be re-directed or sent an email with a link to a payment...", "fullwidth": true}}}