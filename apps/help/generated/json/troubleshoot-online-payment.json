{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/82-troubleshoot-online-payment"], "fullwidth": true, "_template": "contentPage", "title": "Troubleshoot Online Payment", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "If your online payment did not complete successfully after you supplied..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"If your online payment did not complete successfully after you supplied the required credit card and personal information, and you have double-checked that the information is correct, please review the following additional troubleshooting steps:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"If you have set up additional security with your credit card like 3D secure, please make sure that you have the log in details ready BEFORE proceeding as you will be asked for this information to complete the payment. If you are unsure, please call your bank.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"If the above troubleshooting step fail, you can always wire the amount - please review our wiring bank information \", _jsxDEV(_components.a, {\n        href: \"http://help.qasid.com/money-matters/payment-methods\",\n        children: \"here\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 116\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/82-troubleshoot-online-payment"], "fullwidth": true, "_template": "contentPage", "title": "Troubleshoot Online Payment", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "If your online payment did not complete successfully after you supplied..."}}}