{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/102-interactive-listening-assignments"], "title": "Interactive Listening Assignments", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Please log in Canvas and accept the course invitation on your dashboard...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    em: \"em\",\n    img: \"img\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Please log in Canvas and accept the course invitation on your dashboard.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 2,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Go to: \", _jsxDEV(_components.a, {\n        href: \"https://canvas.qasid.com\",\n        children: \"Qasid <PERSON>\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 4,\n        columnNumber: 8\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"If you don't know your password, reset it on the log in page by clicking \", _jsxDEV(_components.em, {\n        children: \"Forgot Password\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 74\n      }, this), \" – make sure to use your Qasid SIS email address\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Go to the course page and click on the assignment for the current week\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-fl3osMo92E.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 10,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Follow the instructions on the assignments page.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Make sure that you Submit your questions as you are answering the questions or at the end. Otherwise, they will NOT be saved when you leave the page. Here is how to submit your questions at any time:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 14,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-Kd22Z90wUC.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 16,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 16,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/102-interactive-listening-assignments"], "title": "Interactive Listening Assignments", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Please log in Canvas and accept the course invitation on your dashboard...", "fullwidth": true}}}