{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/198-using-the-quizlet-mobile-app-to-view-a-quizlet-shared-folder"], "title": "Using the Quizlet Mobile App to view a Quizlet Shared Folder", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "fullwidth": true, "blocks": [{"id": "434631078", "_template": "Vimeo"}]}, "content": {"compiledSource": "\"use strict\";\nconst {jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_components.p, {\n    children: \"Please view the video instructions\"\n  }, undefined, false, {\n    fileName: \"<source.js>\",\n    lineNumber: 2,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/198-using-the-quizlet-mobile-app-to-view-a-quizlet-shared-folder"], "title": "Using the Quizlet Mobile App to view a Quizlet Shared Folder", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "fullwidth": true, "blocks": [{"id": "434631078", "_template": "Vimeo"}]}}}