{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/43-a-note-to-families"], "fullwidth": true, "_template": "contentPage", "title": "A note to families", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "One of the most common concerns we’ve seen in our correspondence with couples who apply..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"One of the most common concerns we’ve seen in our correspondence with couples who apply to Qasid is:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"“We have children, so how can my wife/husband and I both do the course at the same time?”\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Generally speaking, we have two class time-tracks during the day, morning and afternoon. There is a good chance that husbands and wives at different levels will be placed in opposite time-slots, thereby making childcare manageable. For scheduling situations where this does not happen – or if mom and dad simply want to take a break – daycare is available through nurseries in the area.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"For those with children, it is best to bring any specialty items; though Jordan is well-stocked on general creature-comforts, some of the more uncommon items are marked up quite a bit. Please feel free to email us to inquire about what is/isn’t available.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Jordan in general and West Amman in particular are family-oriented; seeing couples walking along with baby-in-stroller is not an uncommon sight. Days off can be spent in nearby city gardens or walking in family-friendly shopping areas. Hospitals and child care are on the whole excellent (see Safety & Health); a list of pediatricians and other specialists is provided to each student during orientation.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/43-a-note-to-families"], "fullwidth": true, "_template": "contentPage", "title": "A note to families", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "One of the most common concerns we’ve seen in our correspondence with couples who apply..."}}}