{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/109-online-placement"], "hero": null, "fullwidth": true, "_template": "contentPage", "title": "Online Placement", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "The online oral placement is made up of one video with a series..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    img: \"img\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"The online oral placement is made up of one video with a series of 10 questions, each in both Arabic and English.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"You are NOT required to provide an oral answer for all the questions, JUST the ones you feel comfortable to answer in ARABIC. At the end of each question, you will pause the video and record your answer for that question and repeat the same procedure for the other questions.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Please email \", _jsxDEV(_components.a, {\n        href: \"mailto:<EMAIL>\",\n        children: \"<EMAIL>\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 14\n      }, this), \" when you have completed the placement.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Below, you will find detailed instructions on accessing the online course and submitting your recordings.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: [\"Go to: \", _jsxDEV(_components.a, {\n          href: \"https://qasid.instructure.com/courses/882801\",\n          children: \"Canvas for Qasid\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 10,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b214e042863193800f66c/file-LxxTWoSiiL.png\",\n          alt: \"Step 2\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 11,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 11,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b215d2c7d3a19436835fe/file-h8ecHjQFPW.png\",\n          alt: \"Step 3\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 12,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b216b042863193800f66d/file-DXdPA7xRpT.png\",\n          alt: \"Step 4\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 13,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 13,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b2177042863193800f66e/file-BRyteX79H3.png\",\n          alt: \"Step 5\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 14,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 14,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b21852c7d3a1943683600/file-VvZbmvuOk8.png\",\n          alt: \"Step 6\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 15,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 15,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b219c2c7d3a1943683601/file-O4ultFuFTb.png\",\n          alt: \"Step 7\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 16,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 16,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b21a82c7d3a1943683603/file-V4dmvvc2Kn.png\",\n          alt: \"Step 8\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 17,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 17,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b21bc2c7d3a1943683604/file-6v13nOLeql.png\",\n          alt: \"Step 9\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 18,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 18,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b21da2c7d3a1943683605/file-PmKuvsYEed.png\",\n          alt: \"Step 9\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 19,\n          columnNumber: 5\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 19,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5a5b21e8042863193800f671/file-3sqBq4OmEv.png\",\n          alt: \"Step 10\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 20,\n          columnNumber: 5\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 20,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Repeat steps 7 – 10 to record your answers for other questions\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 21,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/109-online-placement"], "hero": null, "fullwidth": true, "_template": "contentPage", "title": "Online Placement", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "The online oral placement is made up of one video with a series..."}}}