{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/97-access-survival-arabic-course"], "title": "Access Survival Arabic Course", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Click on this enrollment link\nIf you don't have a Qasid <PERSON> account, follow the arrow in the image below to create an account...\n", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    img: \"img\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: [\"Click on \", _jsxDEV(_components.a, {\n        href: \"https://canvas.qasid.com/enroll/MPEA8D\",\n        children: \"this enrollment link\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 2,\n        columnNumber: 10\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"If you don't have a Qasid <PERSON>vas account, follow the arrow in the image below to create an account:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-OJ2HK2CYLW.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"If you have a Qasid Canvas account, \", _jsxDEV(_components.a, {\n        href: \"https://canvas.qasid.com/enroll/MPEA8D\",\n        children: \"make that selection\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 37\n      }, this), \" and log in with your username and password.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/97-access-survival-arabic-course"], "title": "Access Survival Arabic Course", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Click on this enrollment link\nIf you don't have a Qasid <PERSON> account, follow the arrow in the image below to create an account...\n", "fullwidth": true}}}