{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["http://localhost:3000/help/199-using-anki-app-for-flash-cards"], "title": "Using Anki <PERSON>pp for Flash Cards", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Anki is a popular flash card system and Ankiapp.com provides desktop, mobile and web interfaces for it...", "fullwidth": true, "blocks": [{"id": "455500077", "_template": "Vimeo"}]}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    em: \"em\",\n    li: \"li\",\n    p: \"p\",\n    ul: \"ul\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"Anki is a popular flash card system and Ankiapp.com provides desktop, mobile and web interfaces for it. We've created zip files for our main vocabulary sets that will be sent to you upon request. Below you will find the steps on how to set it up\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: [\"Set up your Anki App account from \", _jsxDEV(_components.a, {\n          href: \"https://www.ankiapp.com/\",\n          children: \"https://www.ankiapp.com/\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 4,\n          columnNumber: 37\n        }, this)]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 4,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"You can use the web app by clicking AnkiApp Web or by downloading the desktop or mobile app and creating your account there\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 5,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: [\"Then, log in at the ankiapp nexus page to import the set that you need to study \", _jsxDEV(_components.a, {\n          href: \"https://api.ankiapp.com/nexus/\",\n          children: \"https://api.ankiapp.com/nexus/\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 6,\n          columnNumber: 83\n        }, this)]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Click Import\\n** Import from Spreadsheet file\\n** Click choose file and select one of the zip files from the folder that was shared with you\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 7,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Note: Avoid trying to import more than one zip file at a time. That can cause upload errors \", _jsxDEV(_components.em, {\n        children: \"You should also type a name for the deck that will be easy for to recognize in future\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 11,\n        columnNumber: 93\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 11,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"If you now go back to AnkiApp Web or any version of the app on the desktop or mobile and sign in your account, you will see the new deck under \\\"decks\\\" icon (second icon from the bottom after \\\"home\\\").\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 13,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Click on the deck to preview it and then click Download\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 15,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Click Review to start studying the set. Most of our decks will have the audio of the word's pronunciation in Arabic.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 16,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Below is the video explanation of the instructions above\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 17,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 15,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["http://localhost:3000/help/199-using-anki-app-for-flash-cards"], "title": "Using Anki <PERSON>pp for Flash Cards", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Anki is a popular flash card system and Ankiapp.com provides desktop, mobile and web interfaces for it...", "fullwidth": true, "blocks": [{"id": "455500077", "_template": "Vimeo"}]}}}