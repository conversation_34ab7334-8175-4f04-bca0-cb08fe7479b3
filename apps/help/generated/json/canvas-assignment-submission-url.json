{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/194-canvas-assignment-submission-website-url"], "title": "Canvas Assignment Submission Website URL", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "After you take a photo or record audio, you will be directed to the Website URL submission ...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    em: \"em\",\n    img: \"img\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"After you take a photo or record audio, you will be directed to the Website URL submission link as shown below.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Sometimes, for some assignments, clicking \", _jsxDEV(_components.em, {\n        children: \"Submit Assignment\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 4,\n        columnNumber: 43\n      }, this), \" does not work.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"In that case, you can go around this by following the steps below:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/canvas-assignment-submission-1.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/canvas-assignment-submission-2.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 10,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/194-canvas-assignment-submission-website-url"], "title": "Canvas Assignment Submission Website URL", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "After you take a photo or record audio, you will be directed to the Website URL submission ...", "fullwidth": true}}}