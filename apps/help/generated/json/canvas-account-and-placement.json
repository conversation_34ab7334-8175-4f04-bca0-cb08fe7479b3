{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/200-new-canvas-account-placement"], "title": "New Canvas Account & Placement", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "When you sign up for the online program, your account will be created in Canvas, our Learning Management System...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    img: \"img\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"When you sign up for the online program, your account will be created in Canvas, our Learning Management System, and you will be added to a placement course (MSA, Classical, or Ammiyya). In this course, you will complete a quiz to help us place you at the appropriate level. Here’s what you can expect.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"You will receive 2 emails:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"(1) Your Canvas account registration email, that will look like the image below. Click on the link to create your account. This will only work if you don't have an account with us \", _jsxDEV(_components.a, {\n        href: \"https://canvas.qasid.com\",\n        children: \"from before\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 181\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-8ISCNsge2R.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"(2) You will receive a course invitation to the placement course, which will look like the image below. Make sure you have an account before clicking on the invitation link. Once you click on it, select \\\"I have an account\\\" and log in.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-bhbIAlGxAM.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/200-new-canvas-account-placement"], "title": "New Canvas Account & Placement", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "When you sign up for the online program, your account will be created in Canvas, our Learning Management System...", "fullwidth": true}}}