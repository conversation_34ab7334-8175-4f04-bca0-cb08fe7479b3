{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/31-jordanian-customs"], "fullwidth": true, "_template": "contentPage", "title": "Jordanian Customs", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Jordanians; whom are naturally hospitable people, commonly express their..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    h2: \"h2\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"Jordanians; whom are naturally hospitable people, commonly express their generosity and hospitality through food. It is not uncommon for strangers to be invited over for lunch or dinner within minutes of an introduction.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"This demeanor is deeply rooted in the hospitality traditions of the Arab world in general; a tradition that stems from a collective experience of the harshness of life and the struggles of living in hostile, barren environments where human contact and social relationships are highly cherished.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Compared to other Middle Eastern countries; nevertheless, <PERSON> may fare on the more liberal side — with some parts being cosmopolitan and very Western. Foreigners are; however, encouraged to exercise caution and common sense whenever they are invited over to a Jordanian residence.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Here is a brief summary of useful things that you may wish to take note of in your social encounters:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"What to Wear?\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Dress modestly. It is best for female guests to be on the conservative side by wearing loose clothing and avoid outfits that reveal the shoulders, upper arms and knees.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Visiting Etiquette and Dining Manners\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 14,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"In many households, residents generally take off their shoes upon entering. Observe the entrance hall and your host. If you notice shoes being stacked outside the door, you are expected to take your shoes off.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 16,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Sandals are sometimes offered to be used in the washroom. These should not be worn outside of the designated area.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 18,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"The Greeting\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 20,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"It is acceptable to shake hands when meeting and being introduced to someone. Do note; however, that some conservative individuals may not wish to shake hands with the opposite sex. It is therefore best to withhold until your counterpart initiates a handshake.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 22,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Ladies; especially those who know each other well, would mutually greet one another by several pecks on the cheek. If a woman offers to greet you this way, be flattered and let her “lead”.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 24,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Whenever someone enters the room, particularly someone older, you are expected to stand. Similarly, do stand when you are being introduced.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 26,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"When invited over for a meal, it is a good gesture to bring a small gift for the household with you. Flowers, chocolates and Arab pastries are good examples.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 28,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"The Meal\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 30,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"In traditional families, men and women commonly dine separately. The guest is always offered the best seat in the house, which is usually at the right of the host.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 32,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Guests are naturally served first and offered the choicest cuts. Don’t be alarmed if a host personally scoops portions of food on your plate for you. Try to eat everything that is served.\\nAvoid eating with your left hand, as it is considered rude.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 34,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Meals usually start with appetizers, or mezze, which are eaten with bread. You can either dip the bread into the mezze plates or take a portion on your individual plate.If you are eating from a common plate, only eat the food that is directly in front of you.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 37,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Conversation Guidelines\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 39,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Jordanians generally hold a deep respect for Islam, the monarchy and their country, so please avoid any conversation that may seem disrespectful or can be easily misconstrued.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 41,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Don’t be alarmed at the multitude of personal questions being asked; i.e. “Are you married?,” “How old are you?,” “Do you have any children?,” and so forth.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 43,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"The Departure\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 45,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Coffee or tea is served after a meal, and after this, guests may take leave if they choose to do so. It is common for the host to accompany the guest to the elevator or even down to the car, while he repeatedly thanks the guest for the visit. He/she will, in any case, remain at the door until the guest is out of sight.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 47,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Works Cited:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 49,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Jordan’s Gastronomic Adventure, Jordanian Etiquette Tips, “Rules and Etiquette When Meeting the Local People,” Ruth’s Jordan Jubilee, Oct. 8, 2010.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 51,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Shaheen, Leila. “Manners in the Middle East”. Saudi Aramco World March/April 1965 (Volume 16:2).\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 53,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/31-jordanian-customs"], "fullwidth": true, "_template": "contentPage", "title": "Jordanian Customs", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Jordanians; whom are naturally hospitable people, commonly express their..."}}}