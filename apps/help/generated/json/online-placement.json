{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/109-online-placement"], "title": "Online Placement", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "hero": null, "description": "The online oral placement is made up of one video with a series of 10 questions...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    img: \"img\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"The online oral placement is made up of one video with a series of 10 questions, each in both Arabic and English.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"You are NOT required to provide an oral answer for all the questions, JUST the ones you feel comfortable to answer in ARABIC. At the end of each question, you will pause the video and record your answer for that question and repeat the same procedure for the other questions.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Please email \", _jsxDEV(_components.a, {\n        href: \"mailto:<EMAIL>\",\n        children: \"<EMAIL>\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 14\n      }, this), \" when you have completed the placement.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Below, you will find detailed instructions on accessing the online course and submitting your recordings.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: [\"Go to: \", _jsxDEV(_components.a, {\n          href: \"https://qasid.instructure.com/courses/882801\",\n          children: \"Canvas for Qasid\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 10,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-LxxTWoSiiL.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 11,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 11,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-h8ecHjQFPW.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 12,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-DXdPA7xRpT.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 13,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 13,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-BRyteX79H3.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 14,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 14,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-VvZbmvuOk8.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 15,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 15,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-O4ultFuFTb.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 16,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 16,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-V4dmvvc2Kn.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 17,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 17,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-6v13nOLeql.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 18,\n          columnNumber: 4\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 18,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-PmKuvsYEed.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 19,\n          columnNumber: 5\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 19,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: _jsxDEV(_components.img, {\n          src: \"/images/file-3sqBq4OmEv.png\",\n          alt: \"\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 20,\n          columnNumber: 5\n        }, this)\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 20,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Repeat steps 7 – 10 to record your answers for other questions\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 21,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/109-online-placement"], "title": "Online Placement", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "hero": null, "description": "The online oral placement is made up of one video with a series of 10 questions...", "fullwidth": true}}}