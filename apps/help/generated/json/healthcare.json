{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/33-what-is-healthcare-like-in-amman"], "fullwidth": true, "_template": "contentPage", "title": "What is healthcare like in Amman?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Fortunately the healthcare facilities and professionals in Jordan are fairly advanced..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"Fortunately the healthcare facilities and professionals in Jordan are fairly advanced. You will find most centers clean and hygienic, the staff friendly and proficient.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"The cost is also very reasonable – a typical doctor’s visit is about 15 JD, and doctors, dentists, and pharmacies are found on many streets.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"There are also emergency and 24 hour services available. Most students do not purchase health care insurance, but if you do have healthcare, you can ask your provider for international coverage.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Qasid also has many native Jordanians on staff in the administration department who are on-call in the case of any emergencies.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/33-what-is-healthcare-like-in-amman"], "fullwidth": true, "_template": "contentPage", "title": "What is healthcare like in Amman?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Fortunately the healthcare facilities and professionals in Jordan are fairly advanced..."}}}