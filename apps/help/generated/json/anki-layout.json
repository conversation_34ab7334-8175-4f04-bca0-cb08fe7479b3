{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/201-anki-changing-layouts-to-display-back-to-front"], "title": "Anki - Changing layouts to display Back to Front", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "How to have your flashcards in Anki display Back first instead of Front first (for ...", "fullwidth": true, "blocks": [{"id": "462521336", "_template": "Vimeo"}]}, "content": {"compiledSource": "\"use strict\";\nconst {jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_components.p, {\n    children: \"How to have your flashcards in Anki display the back first instead of the front (for example, show the English first and then the Arabic when you click \\\"Flip\\\"). We will also demonstrate how to customize your layouts or create new ones.\"\n  }, undefined, false, {\n    fileName: \"<source.js>\",\n    lineNumber: 2,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/201-anki-changing-layouts-to-display-back-to-front"], "title": "Anki - Changing layouts to display Back to Front", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "How to have your flashcards in Anki display Back first instead of Front first (for ...", "fullwidth": true, "blocks": [{"id": "462521336", "_template": "Vimeo"}]}}}