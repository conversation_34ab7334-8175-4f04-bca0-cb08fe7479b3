{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/77-annual-scholarship"], "title": "Annual Scholarship", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "<PERSON><PERSON><PERSON> believes in providing scholarship opportunities for deserving students in recognition of their achievement...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_components.p, {\n    children: \"We do not currently have scholarships on offer. However, we encourage applicants to explore external funding opportunities that may support their studies at Qasid. Academic institutions, employers, embassies, and cultural organizations are often valuable resources for potential sponsorships or financial assistance.\"\n  }, undefined, false, {\n    fileName: \"<source.js>\",\n    lineNumber: 2,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/77-annual-scholarship"], "title": "Annual Scholarship", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "<PERSON><PERSON><PERSON> believes in providing scholarship opportunities for deserving students in recognition of their achievement...", "fullwidth": true}}}