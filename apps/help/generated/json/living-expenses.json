{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/38-how-much-are-living-expenses-in-amman"], "fullwidth": true, "_template": "contentPage", "title": "How much are living expenses in Amman?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "We would like to provide a very general idea about living costs in Jordan so that you may..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"We would like to provide a very general idea about living costs in Jordan so that you may budget appropriately. Please keep in mind that the numbers are estimates, and that the only amounts that we have absolute control over are those pertaining to tuition, books, and the like. The following figures are based on what is common here for a Westerner living on a budget. The extremely frugal and the excessively freewheeling are not included in this range. Having said this, we are confident that this represents a reasonably close estimate to the costs you’ll actually incur, plus or minus 10 to 20%.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"In general, you can use the broad range of 600 JD ($850) to 1150 JD ($1600) per month for all living expenses (including shared housing and utilities, and individual food and city transit) as a base. Please note that the current exchange rate is 1 USD = 0.71 JD (71 qirsh). More specifically, if coming individually, your costs will be on the lower range of this scale depending on where you live. But for those coming with family members costs may exceed 1150 JD per month, factoring in for added expenses like kids schooling, periodic doctor visits, and rising utility costs in the chilly winters. Please see the related questions below for breakdowns and details.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Another cost factor of living in Jordan is the need to renew your visa every 6 months by leaving and returning to the country. For details on this, please see the section below titled:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Do I need to apply for a visa?\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/38-how-much-are-living-expenses-in-amman"], "fullwidth": true, "_template": "contentPage", "title": "How much are living expenses in Amman?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "We would like to provide a very general idea about living costs in Jordan so that you may..."}}}