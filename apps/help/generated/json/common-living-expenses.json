{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/39-what-are-some-other-common-living-expenses-in-amman"], "fullwidth": true, "_template": "contentPage", "title": "What are some other common living expenses in Amman?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Electricity: 30 to 70 JD per household monthly\nWinter heating costs: These vary greatly, depending..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    em: \"em\",\n    li: \"li\",\n    p: \"p\",\n    ul: \"ul\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Electricity: 30 to 70 JD per household monthly\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 2,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Winter heating costs: These vary greatly, depending upon personal temperature preferences, choice of heat source, a home’s architectural thermodynamics, and climatic unpredictability. Central heating in Jordan commonly utilizes a diesel fueled boiler system that circulates hot water through wall mounted radiator panels.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 3,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Monthly fuel costs for this (between December and March) can run anywhere from 100 to 400 JD per household. More affordable options (although limited to single room usage) include electric space heaters and the portable, \", _jsxDEV(_components.em, {\n        children: \"soba\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 5,\n        columnNumber: 222\n      }, this), \" heaters which operate on replaceable, compressed natural gas tanks. Because these appliances have an open flame, however, there are important safety precautions that must be heeded with them. Know too that regardless of which strategy you adopt, the primary construction materials in Jordan are concrete and stone, which means the structures here never seem to get as warm and cozy as they do back home.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 5,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Cab rides: Meters currently start at 25 qirsh and will range between 50 qirsh, called a “nuss”, to 2.5 JD for most trips in and around town. You’ll find buses and shared cabs are much less expensive.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 7,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Wi-Max or ADSL internet: 25 to 55 JD per household per month (well worth it if several people live together); wireless is free for Qasid students and at various malls and coffee shops around town\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Cell phone: Many students bring their own phones if they can have them “unlocked”, and then buy a SIM card and service plan with a local carrier for a one-time cost of about 10 JD. For most students, the easiest and least expensive option is to purchase a new mobile phone here in Amman, starting at around 20 JD, along with a service plan.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 9,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 7,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"If you are curious about the cost of particular other items, please don’t hesitate to ask.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 11,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/39-what-are-some-other-common-living-expenses-in-amman"], "fullwidth": true, "_template": "contentPage", "title": "What are some other common living expenses in Amman?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Electricity: 30 to 70 JD per household monthly\nWinter heating costs: These vary greatly, depending..."}}}