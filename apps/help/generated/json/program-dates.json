{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/19-program-dates"], "fullwidth": true, "_template": "contentPage", "title": "Program Dates", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Qasid runs three, concurrent curricula year-round..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    em: \"em\",\n    h2: \"h2\",\n    li: \"li\",\n    p: \"p\",\n    ul: \"ul\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.h2, {\n      children: \"Overview\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Qasid runs three, concurrent curricula year-round:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Core Programs\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Private Tutoring\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 7,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Diplomat | Corporate | Group Studies\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Core Programs\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Each term consists of 165 to 174 hours. Fall, winter, and spring quarters are 11 to 12 weeks in length (2.5 teaching hours per day), while the summer term is 9 weeks (3.3 teaching hours per day). Each of the core, Modern Standard Arabic and Classical Arabic classes are offered during each of these four terms, while advanced and supplementary courses are available subject to demand. The latest dates are as follows: \", _jsxDEV(_components.a, {\n        href: \"https://www.qasid.com/admissions/program-dates\",\n        children: \"Academic calendar\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 419\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Visit our program dates page for updated information\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 14,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Private Tutoring\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 16,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Though Qasid’s year-round academic schedule was created to complement those of US universities, we recognize that the timing will nevertheless be less than ideal for busy professionals and students whose university schedules don’t coincide with ours. Some will want more a more intensive concentration of hours, while others may want a complete emphasis on either the sciences or skills; for this reason we offer private tutoring.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 18,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Private tutoring is offered year-round on a by-request basis. Tuition rate per teaching hour ranges between $30 to $50, depending on the course timing and if a student partner is involved. Please keep the following points in mind:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 20,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Private lessons may be held in the days or evenings depending upon instructor availability\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 22,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Content can follow our core program curriculum or be custom-tailored\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 23,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Customized courses can focus on purely language, language applied to specific texts, or both\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 24,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Students interested in enrolling for private tutoring should complete an online admissions application form, select “Private Tutoring”, and then await a response from the Admissions Committee\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 25,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: [\"Prospective students wishing to explore this option further may review the related section of the \", _jsxDEV(_components.em, {\n          children: \"Pricing and Payments\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 26,\n          columnNumber: 101\n        }, this), \" document, and|or email us at \", _jsxDEV(_components.a, {\n          href: \"mailto:<EMAIL>\",\n          children: \"<EMAIL>\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 26,\n          columnNumber: 153\n        }, this)]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 26,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Diplomat | Corporate | Group Studies\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 27,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 22,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"These customized academic programs have the same full-flexibility as the private tutoring option described above and can start or end anytime of the year. Coinciding the start date with one of our Core Program start dates, however, will reduce the overall tuition costs. Prospective groups wishing to find out more should contact us at \", _jsxDEV(_components.a, {\n        href: \"mailto:<EMAIL>\",\n        children: \"<EMAIL>\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 29,\n        columnNumber: 337\n      }, this), \".\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 29,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/19-program-dates"], "fullwidth": true, "_template": "contentPage", "title": "Program Dates", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Qasid runs three, concurrent curricula year-round..."}}}