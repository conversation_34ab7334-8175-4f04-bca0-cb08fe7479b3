{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/154-canvas-assignment-submission"], "fullwidth": true, "_template": "contentPage", "title": "Canvas Assignment Submission", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "For writing assignments where you need to send..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    h1: \"h1\",\n    img: \"img\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.h1, {\n      children: \"For writing assignments where you need to send photos or scans of your written work:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"We highly recommend you use the camscanner app (Apple & Android phones) or the Office Lens app (Apple/Android/Windows phones) to take pictures of your work, make a PDF, share the file with yourself and then upload it using the option 1 below (File Upload tab)\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h1, {\n      children: \"For audio/video recording assignments where you need to send a recording of your work\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"If your assignment uses goreact, please refer to these \", _jsxDEV(_components.a, {\n        href: \"https://help.qasid.com/article/192-go-react-student-recordings-and-comments\",\n        children: \"instructions\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 56\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Otherwise, we recommend you use option 3 below (Take Photo or Record Audio tab)\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Click Submit Assignment\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5dd771192c7d3a7e9ae448aa/file-kBiK8fr6i6.png\",\n        alt: \"enrollment\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 14,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 14,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"You have 3 options for submitting your assignment\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 16,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"File Upload: files that you have already scanned/taken pictures/recorded or created like MS Word or Powerpoint\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 18,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Website URL: Link to a cloud folder or file (like dropbox)\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 19,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Take a Photo or Record Audio: Take photo or photos using your device camera or record audio using your camera/microphone and uploaded the converted files all using your browser (not all browsers are supported).\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 20,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 18,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Option 1: File Upload tab (when you need to upload different type of files such as audio and images, meant for mid-term and final)\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 22,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"For creating your own PDF file from scans, we recommend the \", _jsxDEV(_components.a, {\n        href: \"https://www.camscanner.com\",\n        children: \"camscanner app\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 24,\n        columnNumber: 61\n      }, this), \" or office lens app (you can do a search for office lens in the relevant mobile app stores)\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 24,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"For recording audio on your mobile device, we recommend these \", _jsxDEV(_components.a, {\n        href: \"https://www.wikihow.com/Record-Audio-on-a-Mobile-Phone\",\n        children: \"instructions\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 26,\n        columnNumber: 63\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 26,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"If you are using Canvas on your mobile device, you can then select the files directly as shown below. Otherwise, you will need to transfer the files to your computer first \", _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c3c392c7d3a7e9aeb362f/file-bf7mDfUBrF.png\",\n        alt: \"transfer to computer\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 28,\n        columnNumber: 173\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 28,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"Option 2: Your files are on a cloud system like Google Drive or One Drive \", _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c3ccb2c7d3a7e9aeb3632/file-CZ223Mub1c.png\",\n        alt: \"Google or One Drive\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 30,\n        columnNumber: 75\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 30,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Option 3: Recommended for assignments where you need to scan your work – it will combine multiple pictures in one single file\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 32,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c3dd82c7d3a7e9aeb3636/file-eqZBaKY0Zw.png\",\n        alt: \"combine multiple pictures\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 34,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 34,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"3a: Take pictures from your camera and upload\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 36,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c3f0104286364bc989ba6/file-7l8yB9dgWs.png\",\n        alt: \"take pic and upload 1\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 38,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 38,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c3f112c7d3a7e9aeb3645/file-Ix98Yv23op.png\",\n        alt: \"take pic and upload 2\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 40,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 40,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c3f1d2c7d3a7e9aeb3647/file-VRnEEebk5L.png\",\n        alt: \"take pic and upload 3\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 42,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 42,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"3b: Record using your microphone/camera and upload\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 44,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c411004286364bc989bab/file-b67zC8MZn7.png\",\n        alt: \"record 1\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 46,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 46,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c411b2c7d3a7e9aeb3649/file-0SDOfQXCFE.png\",\n        alt: \"record 2\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 48,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 48,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"https://s3.amazonaws.com/helpscout.net/docs/assets/565daa8890336053e408e1fa/images/5e9c412704286364bc989bad/file-Q5d8fnd2sK.png\",\n        alt: \"record 3\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 50,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 50,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/154-canvas-assignment-submission"], "fullwidth": true, "_template": "contentPage", "title": "Canvas Assignment Submission", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "For writing assignments where you need to send..."}}}