{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/207-reset-your-cookies-in-google-chrome"], "title": "Reset your Cookies in Google Chrome", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    img: \"img\",\n    li: \"li\",\n    ol: \"ol\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_components.ol, {\n    children: [\"\\n\", _jsxDEV(_components.li, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-s18IZkuVFd.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 2,\n        columnNumber: 4\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.li, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-GVjCeUBRXe.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 3,\n        columnNumber: 4\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 3,\n      columnNumber: 1\n    }, this), \"\\n\"]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 2,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/207-reset-your-cookies-in-google-chrome"], "title": "Reset your Cookies in Google Chrome", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "fullwidth": true}}}