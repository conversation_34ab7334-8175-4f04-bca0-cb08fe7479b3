{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/127-enabling-arabic-keyboard-on-windows"], "title": "Enabling Arabic Keyboard on Windows", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Change keyboard language in Windows 7 and Vista...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    em: \"em\",\n    h2: \"h2\",\n    img: \"img\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.h2, {\n      children: \"Change keyboard language in Windows 7 and Vista\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"It is fairly simple to add or change a keyboard language in Windows 7 or Vista.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"In Windows 7, enter “keyboard language” in the Start menu search box, and select “Change keyboards or other input methods”.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/sshot20100308185010.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"In Windows Vista, open Control Panel and enter “input language” in the search box and select “Change keyboards or other input methods”. This also works in Windows 7.\", _jsxDEV(_components.img, {\n        src: \"/images/sshot108.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 10,\n        columnNumber: 166\n      }, this)]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Now, click \\\"Change Keyboards\\\" to add another keyboard language or change your default language.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image54.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 14,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 14,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Our default input language is US English, and the default keyboard layout is the US layout.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 16,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Click \\\"Add\\\" to insert another input language while keeping your default input language installed.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 18,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image55.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 20,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 20,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Here we selected the standard Thai keyboard language (Thai Kedmanee), but you can select any language you want.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 22,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Windows offers almost any language you can imagine, so just look for the language you want, select it, and click Ok.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 24,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image56.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 26,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 26,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Alternatively, if you prefer, you can click \\\"Preview\\\" to see your layout choice before accepting it.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 28,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"These are only the default characters, not the ones activated with Shift or other keys (many Asian languages use more characters than English and require the use of Shift and other keys to access them all).\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 30,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Once you're finished previewing, click \\\"Close\\\" and then press \\\"OK\\\" on the previous dialog.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 32,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image57.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 34,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 34,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Now you will see both of your keyboard languages in the Installed services box.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 36,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"You can click \\\"Add\\\" to select more languages, adjust the priority by moving your selected language up or down, or simply click \\\"Apply\\\" to add the new language.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 38,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image58.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 40,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 40,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Also, you can now change the default input language from the top menu.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 42,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"This is the language that your keyboard will start with when you boot your computer.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 44,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"So, if you mainly use English but also use another language, usually it is best to leave English as your default input language.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 46,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image59.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 48,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 48,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Once you’ve pressed Apply or Ok, you will see a new icon beside your system tray with the initials of your default input language.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 50,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image60.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 52,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 52,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"If you click it, you can switch between input languages.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 54,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Alternately you can switch input languages by pressing Alt+Shift on your keyboard.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 56,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image61.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 58,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 58,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Some complex languages, such as Chinese, may have extra buttons to change input modes to accommodate their large alphabet.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 60,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image62.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 62,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 62,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"If you would like to change the keyboard shortcut for changing languages, go back to the Input Languages dialog, and select the “Advanced Key Settings” tab.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 64,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Here you can change settings for Caps Lock and change or add key sequences to change between languages.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 66,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image63.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 68,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 68,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Also, the On-Screen keyboard will display the correct keyboard language (here the keyboard is displaying Thai), which can be a helpful reference if your physical keyboard doesn’t have your preferred input language printed on it.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 70,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"To open this, simply enter “On-Screen keyboard” in the start menu search, or click \", _jsxDEV(_components.em, {\n        children: \"All Programs Accessories\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 72,\n        columnNumber: 84\n      }, this), \" On-Screen keyboard.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 72,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/image64.jpeg\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 74,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 74,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/127-enabling-arabic-keyboard-on-windows"], "title": "Enabling Arabic Keyboard on Windows", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "Change keyboard language in Windows 7 and Vista...", "fullwidth": true}}}