{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/128-enabling-arabic-keyboard-on-mac"], "fullwidth": true, "_template": "contentPage", "title": "Enabling Arabic Keyboard on Mac", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "How to enable the Arabic keyboard on Mac..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    em: \"em\",\n    h2: \"h2\",\n    li: \"li\",\n    p: \"p\",\n    ul: \"ul\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.h2, {\n      children: \"How to enable the Arabic keyboard on Mac\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"All OS X versions are able to display Arabic perfectly fine. However, if you wish to \", _jsxDEV(_components.em, {\n        children: \"type\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 4,\n        columnNumber: 86\n      }, this), \" in Arabic you must enable an Arabic keyboard layout.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Doing so is simple:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Go to System Preferences... from the Apple  menu in the top left corner.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 8,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Then:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"** If using OS X >= 10.9: click on Keyboard\\n** If using OS X <= 10.9: click on Language Text\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Click on the Input Sources tab.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 15,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"In the left sidebar, locate the \\\"Arabic\\\" entry and select it. Or for OS X >= 10.9, click on the + icon, locate the \\\"Arabic\\\" entry and add it.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 16,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Now, whenever you need to type Arabic, you'll be able to select the Arabic keyboard from the menu bar. English is denoted by either the U.S flag or similar. Arabic is denoted by the green crescent or isolated ع depending on your OS X version.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 17,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 15,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Use a shortcut to switch between languages:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 19,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"In OS X >= 10.9: go to System Preferences - Keyboard Shortcuts. On the left sidebar, click on Input Source.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 21,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 21,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"In OS X <= 10.9: go to System Preferences -> Language Text -> Input Sources. Click on the Keyboard Shortcuts... button.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 23,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ul, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Here you can define a shortcut for switching to the previous/next input source language. We like to use the default ⌘Space for this purpose.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 25,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Enabling Automatically switch to a document's input source will allow you to maintain an input language per document. So you can for example open 2 documents, write one in English and the other in Arabic, and OS X will remember the keyboard association for each.\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 26,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 25,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Using the Keyboard Viewer\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 28,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"If your physical Mac keyboard doesn't contain Arabic symbols, you can \", _jsxDEV(_components.a, {\n        href: \"http://www.amazon.com/dp/B00AVBLGZG/ref=cm_sw_r_tw_dp_rD4tub0PG0A4D\",\n        children: \"purchase\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 30,\n        columnNumber: 71\n      }, this), \" \", _jsxDEV(_components.a, {\n        href: \"http://www.amazon.com/dp/B000ZZ1BDA/ref=cm_sw_r_tw_dp_Lo4tub0P9BZ7X\",\n        children: \"stickers\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 30,\n        columnNumber: 151\n      }, this), \" \", _jsxDEV(_components.a, {\n        href: \"http://www.amazon.com/dp/B002PDH1EI/ref=cm_sw_r_tw_dp_3E4tub1XSZ25X\",\n        children: \"online\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 30,\n        columnNumber: 231\n      }, this), \" and lay \", _jsxDEV(_components.a, {\n        href: \"http://www.4keyboard.com/mac-apple-stickers-arabic-c-124_125.html\",\n        children: \"them\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 30,\n        columnNumber: 317\n      }, this), \" on top of your keyboard.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 30,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Alternatively, you can use Keyboard Viewer, which will you show a virtual keyboard for the currently selected input language.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 32,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Learners of Arabic will like this\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 34,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Learners of Arabic will find the \\\"Arabic QWERTY\\\" keyboard layout useful. What it does is transliterate Latin sounds into Arabic. So you can type \\\"A\\\" and it will produce the Arabic character most closely associated with that sound, which is the Arabic Alef ا. And so forth for the rest of the alphabet.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 36,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"To add the \\\"Arabic QWERTY\\\" keyboard layout, follow the steps above for adding \\\"Arabic\\\", but add \\\"Arabic QWERTY\\\" instead.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 38,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/128-enabling-arabic-keyboard-on-mac"], "fullwidth": true, "_template": "contentPage", "title": "Enabling Arabic Keyboard on Mac", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/online.md", "description": "How to enable the Arabic keyboard on Mac..."}}}