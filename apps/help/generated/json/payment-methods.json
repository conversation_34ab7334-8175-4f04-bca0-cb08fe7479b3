{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/81-payment-methods"], "fullwidth": true, "_template": "contentPage", "title": "Payment Methods", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Qasid currently accepts three forms of payment, including cash in US dollars..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    em: \"em\",\n    h2: \"h2\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"Qasid currently accepts three forms of payment, including cash in US dollars or the equivalent in Jordanian dinars (the current exchange rate is approximately 1 USD to 0.71 JOD), wire transfer, or credit card (VISA or MasterCard). You may contact Qasid Admissions with any other questions about credit card payments, while the protocols for performing wire transfers are explained in full detail below.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Online Payment\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"You can pay directly online using VISA or Mastercard at the following \", _jsxDEV(_components.a, {\n        href: \"http://www.qasid.com/admissions/online-payment\",\n        children: \"link\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 6,\n        columnNumber: 71\n      }, this), \".\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Wire Instructions\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 8,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Perhaps the most common form of student payment is via electronic, or “wire”, transfer between banks. Please keep in mind that because your bank and any intermediate, corresponding bank will likely withhold a commission for their services, you will need to send a total amount equal to the actual deposit or tuition PLUS your estimated commission fees in order for us to receive the required funds in full. This transaction cost can typically be anywhere from $10 to $40 per bank, depending on the bank’s regulations, so contact your bank for more information. In the event that Qasid does not receive the full amount, the remaining balance will be included in a subsequent invoice. However, Qasid covers the fee that its own bank charges for receiving funds, which is variable but never exceeds $5 USD. So subsequent invoices for under paid deposits will only be issued if the amount that arrives is below $345.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 10,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: [\"In addition, once you have initiated the transfer, \", _jsxDEV(_components.em, {\n        children: \"please email Qasid Admissions\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 12,\n        columnNumber: 52\n      }, this), \" with a transaction, reference, or confirmation number along with name of the student(s) for whose enrollment the funds are intended. This will allow us to verify that the deposit or tuition has indeed reached us within the deadline. This email is especially important if the person under whose name the transfer is made differs from your own (or the student(s) on whose behalf the funds are being sent). We cannot guarantee that you will receive credit for the payment if we cannot validate for whom it has been made.\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 12,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Provided here below then, are two tables with all the information you should need for wire transfers.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 14,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Wiring Information for Sending US Dollars\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 16,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Qasid’s Bank\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 18,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Name of Beneficiary: The Jordanian American Language Academy\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 20,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Queen Rania StreetSports City, Amman 11196Jordan\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 22,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Account number 0125 257949 9 510\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 24,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Name of Bank Arab Bank PLC\\nUniversity Street BranchP.O. Box 963200\\nPostal Code 11196\\nAmman, Jordan\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 26,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Swift Code | BIC ARABJOAX100\\nIBAN JO77 ARAB 1250 0000 0012 5257 9495 10\\nReference Section Please put something to the effect of: “Tuition deposit from [Student name]” or “Tuition total from [Student name]”\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 31,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.h2, {\n      children: \"Correspondent Bank\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 35,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Correspondent Bank Name Credit Suisse Bank\\nCorrespondent Bank Address Zurich, Switzerland\\nSwift Code | BIC CRESCHZZ80A (Note that the second to last character is the digit ‘0’ and NOT the letter ‘O’.)\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 37,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Please know that these tables supply far more information than what most banks need for outgoing wire transfers. It is shared, however, for the sake of completeness. By far the most important numbers are:\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 41,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"The two Swift codes (both for the Arab Bank and correspondent bank), and Qasid’s account number\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 43,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Rest assured that your bank should be able to perform this transaction, regardless of which country the transfer is initiated from. If your bank has any difficulties understanding the information we’ve provided, as always, please email Admissions for assistance.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 45,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/81-payment-methods"], "fullwidth": true, "_template": "contentPage", "title": "Payment Methods", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "Qasid currently accepts three forms of payment, including cash in US dollars..."}}}