{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/84-create-an-account-in-student-portal"], "title": "Create an account in Student Portal", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "To register, go to Student Information System...", "fullwidth": true}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    a: \"a\",\n    img: \"img\",\n    li: \"li\",\n    ol: \"ol\",\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: [\"To register, go to \", _jsxDEV(_components.a, {\n          href: \"https://sis.qasid.com\",\n          children: \"Student Information System\"\n        }, undefined, false, {\n          fileName: \"<source.js>\",\n          lineNumber: 2,\n          columnNumber: 23\n        }, this)]\n      }, undefined, true, {\n        fileName: \"<source.js>\",\n        lineNumber: 2,\n        columnNumber: 1\n      }, this), \"\\n\", _jsxDEV(_components.li, {\n        children: \"Click on the confirmation link in the automatic e-mail confirmation\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 3,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-29TMe4cv4M.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 5,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 5,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.ol, {\n      children: [\"\\n\", _jsxDEV(_components.li, {\n        children: \"Log in and follow the admission submission steps\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 7,\n        columnNumber: 1\n      }, this), \"\\n\"]\n    }, undefined, true, {\n      fileName: \"<source.js>\",\n      lineNumber: 7,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: _jsxDEV(_components.img, {\n        src: \"/images/file-6zQsUzTJoh.png\",\n        alt: \"\"\n      }, undefined, false, {\n        fileName: \"<source.js>\",\n        lineNumber: 9,\n        columnNumber: 1\n      }, this)\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 9,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/84-create-an-account-in-student-portal"], "title": "Create an account in Student Portal", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "To register, go to Student Information System...", "fullwidth": true}}}