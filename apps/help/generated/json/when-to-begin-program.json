{"metadata": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/20-when-can-i-begin-the-program"], "fullwidth": true, "_template": "contentPage", "title": "When can I begin the program?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "For anyone who is serious about their Arabic, making a 9 to 12 month..."}, "content": {"compiledSource": "\"use strict\";\nconst {Fragment: _Fragment, jsxDEV: _jsxDEV} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    ..._provideComponents(),\n    ...props.components\n  };\n  return _jsxDEV(_Fragment, {\n    children: [_jsxDEV(_components.p, {\n      children: \"For anyone who is serious about their Arabic, making a 9 to 12 month commitment is a strong step in the right direction. Our programs are geared towards busy professionals and students taking limited time off from their university schedules.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 2,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"Because of this, we make it a point to offer each core level (1 to 5) in each and every session throughout the academic year. What this means is that you can begin at Qasid at the start of any of our seasonal quarters: winter, spring, summer, or fall.\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 4,\n      columnNumber: 1\n    }, this), \"\\n\", _jsxDEV(_components.p, {\n      children: \"If summer is over capacity, come in the fall; if fall is impossible, we’ll welcome you in the winter. If winter doesn’t work, you’re invited to start in the spring. Breaks between each term allow plenty of time for rest and a quick recharge. This is also an ideal time to take a side-trip to neighboring countries (regional politics permitting).\"\n    }, undefined, false, {\n      fileName: \"<source.js>\",\n      lineNumber: 6,\n      columnNumber: 1\n    }, this)]\n  }, undefined, true, {\n    fileName: \"<source.js>\",\n    lineNumber: 1,\n    columnNumber: 1\n  }, this);\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsxDEV(MDXLayout, {\n    ...props,\n    children: _jsxDEV(_createMdxContent, {\n      ...props\n    }, undefined, false, {\n      fileName: \"<source.js>\"\n    }, this)\n  }, undefined, false, {\n    fileName: \"<source.js>\"\n  }, this) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n", "frontmatter": {}, "scope": {"published": "2024-11-05T00:00:00.000Z", "redirect_from": ["https://help.qasid.com/article/20-when-can-i-begin-the-program"], "fullwidth": true, "_template": "contentPage", "title": "When can I begin the program?", "author": "content/authors/qasid.md", "sidebars": "content/sidebars/amman.md", "description": "For anyone who is serious about their Arabic, making a 9 to 12 month..."}}}