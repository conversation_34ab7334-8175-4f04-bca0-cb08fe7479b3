import Image from "next/image"
import { classNames } from "@/lib/utils"

import { PageBlocksWelcomeHero } from "@/tina/__generated__/types"
import { tinaField } from "tinacms/dist/react"
import { TinaMarkdown } from "tinacms/dist/rich-text"

import { SearchSection } from "@/components/page/search-section"

export function WelcomeHero( props: { 
  data: PageBlocksWelcomeHero
  handleSubmit?: Function
  searchTerm?: string
  setSearchTerm?: Function
}) {
  const { data, handleSubmit, searchTerm, setSearchTerm } = props
  const backgroundColor = data.backgroundColor || "#212020"
  const backgroundImage = data.backgroundImage
    ? `${data.backgroundImage}`
    : "/images/<EMAIL>"
  return (
    <>
      <section
        className={classNames( data.links 
          ? "h-fit"
          : "h-[36vh]", 
          "relative flex w-full items-center justify-center" )}
        style={{ backgroundColor }}
        data-tina-field={tina<PERSON>ield( data, "backgroundColor" )}
      >
        {backgroundImage !== "none" && (
          <div className="absolute inset-0 overflow-hidden">
            <Image
              alt={data.title || ""}
              className="size-full object-cover opacity-50"
              height={1080}
              src={backgroundImage}
              style={{ aspectRatio: "1920/1080", objectFit: "cover" }}
              width={1920}
              data-tina-field={tinaField( data, "backgroundImage" )}
            />
          </div>
        )}
        <div className="z-5 sm:align-center relative max-w-3xl px-4 text-center">
          <h1 className="font-sans text-lg text-accent sm:my-4 sm:pt-8 sm:text-5xl sm:font-semibold" data-tina-field={tinaField( data, "title" )}>
            {data.title ?? "Qasid Knowledge Base"}
          </h1>
          <div className="max-w-none text-sm text-accent sm:text-lg" data-tina-field={tinaField( data, "message" )}>
            <TinaMarkdown content={data.message} />
          </div>
          <div className="pb-10">
            <SearchSection 
              handleSubmit={handleSubmit} 
              searchTerm={searchTerm} 
              setSearchTerm={setSearchTerm} 
            />
          </div>
        </div>
      </section>
    </>
  )
}
