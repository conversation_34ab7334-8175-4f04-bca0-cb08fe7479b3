import Image from "next/image"
import { PageBlocksCoverSection } from "@/tina/__generated__/types"
import { tinaField } from "tinacms/dist/react"
import { TinaMarkdown } from "tinacms/dist/rich-text"

export function CoverSection( props: PageBlocksCoverSection ) {
  const backgroundImage = props.backgroundImage
    ? `${props.backgroundImage}`
    : "none"
  const backgroundColor = props.backgroundColor || "#ffffff"
  
  return (
    <>
      <section className="relative mt-12" data-tina-field={tinaField( props, "backgroundColor" )}>
        <div className={`absolute inset-0 flex flex-col items-center justify-center`}>
          <h3 className="px-4 text-center text-lg font-bold text-muted sm:text-2xl md:text-3xl" data-tina-field={tinaField( props, "headline" )}>
            {props.headline || ""}
          </h3>
          <div className="prose text-white" data-tina-field={tinaField( props, "content" )}>
            <TinaMarkdown content={props.content} />
          </div>
        </div>
      </section>
    </>
  )
}
