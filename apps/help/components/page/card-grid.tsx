import Image from "next/image";
import Link from "next/link";
import { PageBlocksCardgrid } from "@/tina/__generated__/types";
import { tinaField } from "tinacms/dist/react";
import { TinaMarkdown } from "tinacms/dist/rich-text";

import { But<PERSON> } from "@/components/ui/button";

export function CardGrid(props: PageBlocksCardgrid) {
  const { cardblock } = props;

  return (
    <div className="max-w-max px-6 pt-2">
      {cardblock && cardblock?.length > 0 && (
        <div className="align-items-top my-8 grid max-w-max flex-auto justify-center gap-4 py-4 sm:grid-cols-4">
          {cardblock.map((item, i) => {
            const backgroundImage = item?.coverimage
              ? `${item?.coverimage}`
              : "none";
            return (
              <div
                className="overflow-hidden rounded-lg border bg-card shadow-sm"
                key={item?.headline}
              >
                {backgroundImage !== "none" && (
                  <Image
                    alt={item?.headline as string}
                    className="h-[250px] w-full object-cover sm:h-[400px]"
                    height={300}
                    src={backgroundImage}
                    data-tina-field={tinaField(item, "coverimage")}
                    style={{
                      aspectRatio: "400/300",
                      objectFit: "cover",
                    }}
                    width={400}
                  />
                )}

                <div className="p-4">
                  <h3
                    className="mb-2 text-xl font-bold"
                    data-tina-field={tinaField(item, "headline")}
                  >
                    {item?.headline as string}
                  </h3>

                  <div
                    className="prose mb-4 text-gray-600"
                    data-tina-field={tinaField(item, "content")}
                  >
                    <TinaMarkdown content={item?.content} />
                  </div>

                  {item?.links && item?.links.length > 0 && (
                    <div className="flex items-center justify-end gap-2">
                      {item?.links.map((linkItem) => (
                        <Link
                          href={linkItem?.link || ""}
                          data-tina-field={tinaField(linkItem, "link")}
                          key={linkItem?.link}
                        >
                          {linkItem?.style === "button" ? (
                            <Button
                              variant="secondary"
                              data-tina-field={tinaField(linkItem, "label")}
                            >
                              {linkItem?.label}
                            </Button>
                          ) : (
                            <div
                              className="font-semibold text-primary"
                              data-tina-field={tinaField(linkItem, "label")}
                            >
                              {linkItem?.label}
                            </div>
                          )}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
