"use client"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"

export function SearchSection( props: {
  handleSubmit?: Function | any
  searchTerm?: string
  setSearchTerm?: Function | any
}) {
  const { handleSubmit, searchTerm, setSearchTerm } = props

  return (
    <form action={handleSubmit} className="flex space-x-2">
      <Input
        type="text"
        name="searchTerm"
        placeholder="Search the knowledge base..."
        value={searchTerm}
        onChange={( e ) => setSearchTerm( e.target.value )}
        className="grow border-primary bg-white ring-primary focus:border-primary"
      />
      <Button type="submit">Search</Button>
    </form>
  )
}
