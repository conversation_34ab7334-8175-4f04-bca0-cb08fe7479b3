import React from "react"
import Link from "next/link"
import Image from "next/image"
import { PageAndNavQuery } from "@/tina/__generated__/types"
import {
  Facebook,
  MapPin,
  InstagramIcon,
  Twitter,
  Youtube,
} from "lucide-react"
import { tinaField } from "tinacms/dist/react"
import { useCountry } from "@/hooks/use-country"

import { buttonVariants } from "@/components/ui/button"
import footerLogo from "@/public/<EMAIL>"

function objectEntriesFilter(
  obj: { [s: string]: unknown } | ArrayLike<unknown>
) {
  return Object.entries( obj )
    .filter( ( [key, value] ) =>
      value !== null &&
      value !== undefined &&
      value !== "" &&
      Object.keys( platformLinks ).includes( key )
    ).map( ( [key, value] ) => ({ platform: key, handle: value }) )
}

const platformLinks = {
  googlemaps: "https://goo.gl/maps",
  twitter: "https://twitter.com",
  facebook: "https://facebook.com",
  youtube: "https://youtube.com",
  instagram: "https://instagram.com"
}

type PlatformKey = keyof typeof platformLinks
const getLink = ( platform: PlatformKey ): string => {
  return platformLinks[platform]
}

type SocialIconProps = { platform: string, size?: number }
function SocialIcon({ platform, size = 24 }: SocialIconProps ) {
  const iconProps = {
    size: size,
    className: "text-gray-600 hover:text-gray-800 transition-colors"
  }

  switch ( platform.toLowerCase() ) {
    case "twitter":
      return <Twitter {...iconProps} />
    case "facebook":
      return <Facebook {...iconProps} />
    case "instagram":
      return <InstagramIcon {...iconProps} />
    case "googlemaps":
      return <MapPin {...iconProps} />
    case "youtube":
      return <Youtube {...iconProps} />
    default:
      return <Facebook {...iconProps} />
  }
}

const HOME_SITE_URL = "https://qasid.com"

export const Footer = ({ footer }: { footer: PageAndNavQuery["footer"] }) => {
  const { isTurkey } = useCountry()
  const year = new Date().getFullYear()
  const social = footer.social ? objectEntriesFilter( footer.social ) : null
  const localizedHref = isTurkey && HOME_SITE_URL.includes( ".com" ) 
    ? HOME_SITE_URL.replace( ".com", ".tr" ) 
    : HOME_SITE_URL

  return (
    <footer className="bg-[#212020] py-8">
      <div className="container mx-auto px-4 max-w-[1024px] flex flex-col justify-between min-h-[200px]">
        {/* Top section - Footer links */}
        <div className="grid grid-cols-1 sm:flex justify-center sm:space-x-12">
          <div className="space-y-4 sm:space-y-1">
            <div className="block gap-x-4 space-y-1">
              <div className="mb-2.5 text-sm uppercase text-[#6b6b6b]">Important links</div>
              {footer.qasid &&
                footer.qasid.length > 0 &&
                footer.qasid.map( ( item: any, index: number ) => (
                  <div key={index}>
                    <a
                      key={index}
                      target="_blank"
                      rel="noreferrer"
                      href={item.link}
                      className="text-sm font-semibold uppercase text-[#b6b6b6] hover:text-white hover:underline text-left"
                    >
                      {item?.title}
                    </a>
                  </div>
                ))}
            </div>
          </div>
          
          <div className="mt-6 sm:mt-0 gap-x-4 space-y-2 sm:space-y-1">
            {footer.links &&
              footer.links.length > 0 &&
              footer.links.map((item: any, index: number) => (
                <div key={index}>
                  <a
                    target="_blank"
                    rel="noreferrer"
                    href={item.link}
                    key={index}
                    className="text-sm font-semibold uppercase text-[#b6b6b6] hover:text-white hover:underline text-left"
                  >
                    {item?.title}
                  </a>
                </div>
              ))}
          </div>

          <div className=" gap-x-4 space-y-6">
            <div className="mt-8 sm:mt-0 max-w-3/4 w-1/2 sm:w-[150px]">
              <a
                href={localizedHref}
                target="blank">
                <Image 
                  quality={90}
                  src={footerLogo || "/icon.svg"} 
                  alt="Qasid logo" 
                />
              </a>
            </div>
            <div className="sm:mt-6">
              <nav className="flex items-center space-x-1">
                {social &&
                  social.map( ( item ) => {
                    const platformLink = getLink( item.platform as PlatformKey )
                    return (
                      <Link
                        href={`${platformLink}/${item?.handle}`}
                        key={platformLink}
                        target="_blank"
                        rel="noreferrer"
                        data-tina-field={tinaField( footer, "social" )}
                      >
                        <div className={buttonVariants({ size: "sm", variant: "ghost" })}>
                          <SocialIcon platform={item.platform} />
                          <span className="sr-only">{item?.platform}</span>
                        </div>
                      </Link>
                    )
                  })}
              </nav>
            </div>
          </div>
        </div>

        {/* Bottom section - Copyright and social links */}
        <div className="flex flex-col mt-6 sm:mt-0 items-center space-y-6">
          <p 
            className="sm:mt-6 text-sm leading-5 text-accent text-center" 
            data-tina-field={tinaField( footer, "copyright" )}
          >
            &copy; 2001 – {year} {footer.copyright}
          </p>
        </div>
      </div>
    </footer>
  )
}
