"use client"

import * as React from "react"
import Image from "next/image"
import Link from "next/link"
import { PageAndNavQuery } from "@/tina/__generated__/types"
import { Menu } from "lucide-react"
import { tinaField } from "tinacms/dist/react"
import { useCountry } from "@/hooks/use-country"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Footer,
  DialogTrigger
} from "@/components/ui/dialog"
import { ThemeToggle } from "@/components/theme-toggle"

const HOME_SITE_URL = process.env.NEXT_PUBLIC_HELP_URL || "https://help.qasid.com"

export function SiteHeader({ nav, header }: {
  nav: PageAndNavQuery["nav"]
  header: PageAndNavQuery["header"]
}) {
  const { isTurkey } = useCountry()
  const localizedHref = isTurkey && HOME_SITE_URL.includes( ".com" ) 
    ? HOME_SITE_URL.replace( ".com", ".tr" ) 
    : HOME_SITE_URL

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background">
      <div className="container flex h-16 items-center">
        <Link href="/" className="flex items-center gap-1">
          <div
            style={{ position: "relative", width: "120px", height: "80px" }}
            className="hover:bg-transparent"
            data-tina-field={header.logo && tinaField( header, "logo" )}
          >
            <Image
              src={header.logo || ""}
              alt={header.siteTitle || ""}
              sizes="150px"
              fill
              style={{ objectFit: "contain" }}
            />
          </div>
          {header.logoTitle && (
            <div
              className="px-2 font-normal"
              data-tina-field={header.logo && tinaField( header, "logoTitle" )}
            >
              {header.logoTitle}
            </div>
          )}
        </Link>
        <div
          className={`hidden grow ${Boolean( header.navAlignment ) && `justify-end`} md:flex`}
        >
          <ul className="flex items-center gap-3 p-6">
            {nav.links?.map( ( link ) => {
              let navLink = ""
              let isExternal = false
              if ( link?.linkType === "page" ) {
                navLink = localizedHref + "/" + link.linkedPage?._sys.breadcrumbs.join( "/" ) || ""
              }
              if ( link?.linkType === "relative" ) {
                navLink = link.link || ""
              }
              if ( link?.linkType === "external" ) {
                navLink = link.link || ""
                isExternal = true
              }
              return (
                <li
                  data-tina-field={link && tinaField( link, "label" )}
                  key={link?.link}
                  className="row-span-3"
                >
                  <Link href={navLink} target={isExternal ? "_blank" : ""}>
                    <Button variant="secondary">{link?.label}</Button>
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>
        <div className="flex flex-1 items-center justify-end space-x-4 md:hidden">
          <Dialog>
            <DialogTrigger asChild className="block md:hidden">
              <Button
                variant="ghost"
                className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
              >
                <Menu className="size-6" />
                <span className="sr-only">Toggle Menu</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="flex flex-col justify-center py-12 sm:max-w-[425px]">
              {nav.links?.map( ( link ) => {
                let navLink = ""
                let isExternal = false
                if ( link?.linkType === "page" ) {
                  navLink = localizedHref + "/" + link.linkedPage?._sys.breadcrumbs.join( "/" ) || ""
                }
                if ( link?.linkType === "relative" ) {
                  navLink = link.link || ""
                }
                if ( link?.linkType === "external" ) {
                  navLink = link.link || ""
                  isExternal = true
                }
                return (
                  <Link
                    key={link?.link}
                    href={navLink}
                    target={isExternal ? "_blank" : ""}
                    data-tina-field={link && tinaField( link, "label" )}
                  >
                    <Button variant="ghost" className="w-full text-lg">
                      {link?.label}
                    </Button>
                  </Link>
                )
              })}
              {header.darkmode && (
                <DialogFooter>
                  <div className="flex w-full justify-center md:hidden">
                    <ThemeToggle />
                  </div>
                </DialogFooter>
              )}
            </DialogContent>
          </Dialog>
          {header.darkmode && (
            <div className="hidden md:flex">
              <ThemeToggle />
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
