"use client"
import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image"

import {
  PageAndNavQuery,
  PageBlocksFeaturedPostsPosts,
} from "@/tina/__generated__/types"
import { useTina } from "tinacms/dist/react"

import { searchPosts } from "@/lib/get-posts"
import { SearchSection } from "@/components/page/search-section"

import { Footer } from "@/components/footer"
import { CardGrid } from "@/components/page/card-grid"
import { CoverSection } from "@/components/page/cover-section"
import { FeaturedPosts } from "@/components/page/featured-posts"
import { PageContent } from "@/components/page/page-content"
import { SiteHeader } from "@/components/site-header"

import { SearchIndexPageComponent } from "@/components/app/search-page"
export const dynamic = "force-dynamic"

export function PageComponent( props: {
  data: PageAndNavQuery
  variables: { relativePath: string }
  query: string
}) {
  const { replace } = useRouter();
  const searchParams = useSearchParams()
  const search = searchParams.get( "searchTerm" ) as string

  const { data } = useTina( props )
  const [searchTerm, setSearchTerm] = useState( "" )
  const [results, setResults] = useState<any>( null )

  const backgroundColor = "#212020"
  const backgroundImage = "/images/<EMAIL>"
  
  const handleSearch = ( async ( formData: FormData ) => {
    const term = formData.get( "searchTerm" ) as string
    const posts = await searchPosts( term )
    setResults( posts )
    replace( `?searchTerm=${term}` )
  })

  return (
    <>
      <SiteHeader nav={data.nav} header={data.header} />
      <div className="flex flex-col">
        <div className="grow">
          <section className="relative flex h-fit w-full items-center justify-center py-6" style={{ backgroundColor }}>
            {backgroundImage && (
              <div className="absolute inset-0 overflow-hidden">
                <Image
                  alt="Qasid Knowledge Base"
                  className="size-full object-cover opacity-50"
                  height={1080}
                  src={backgroundImage}
                  style={{ aspectRatio: "1920/1080", objectFit: "cover" }}
                  width={1920}
                />
              </div>
            )}
            <div className="z-5 sm:align-center relative max-w-3xl px-4 text-center">
              <h1 className="font-monserrat text-lg text-accent sm:my-4 sm:pt-8 sm:text-5xl sm:font-semibold">
                Knowledge Base
              </h1>
              <div className="max-w-none pb-4 text-sm text-accent sm:text-lg">
                What can we help you with?
              </div>
              <div className="pb-10">
                <SearchSection 
                  handleSubmit={handleSearch} 
                  searchTerm={searchTerm} 
                  setSearchTerm={setSearchTerm} 
                />
              </div>
            </div>
          </section>

          {( search || results ) ? (
            <SearchIndexPageComponent results={results} />
          ) : (
            <>
              {data.page.blocks?.map( ( block, i ) => {
                switch ( block?.__typename ) {
                  case "PageBlocksCardgrid": {
                    return <CardGrid key={i} {...block} />
                  }
                  case "PageBlocksCoverSection": {
                    return <CoverSection key={i} {...block} />
                  }
                  case "PageBlocksFeaturedPosts": {
                    return (
                      <FeaturedPosts
                        key={i}
                        posts={block.Posts as PageBlocksFeaturedPostsPosts[]}
                      />
                    )
                  }
                  case "PageBlocksPageContent": {
                    return <PageContent key={i} {...block} />
                  }
                }
              })}
            </>
          )}
        </div>
        <Footer footer={data.footer} />
      </div>
    </>
  )
}
