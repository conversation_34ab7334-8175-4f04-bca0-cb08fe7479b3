"use client"
import { SearchResults } from "@/lib/types"

export function SearchIndexPageComponent( props: { results: SearchResults }) {
  const { results } = props

  return (
    <>      
      <div className="mb-6 w-full bg-white">
        <h1 className="text-center font-sans text-lg text-muted sm:mb-4 sm:pt-8 sm:text-2xl sm:font-semibold">Search Results</h1>

        <div className="flex items-center p-8">
          {( !results || results.length < 1 ) ? (
            <h2>No results found</h2>
          ) : (
            <ul role="list" className="divide-y divide-gray-100">
              {results.map( ( searchRes ) => (
                <li key={searchRes.link} className="flex gap-x-4 py-5">
                  <div className="flex-auto">
                    <div className="flex items-baseline justify-between gap-x-4">
                      <p className="text-sm/6 font-semibold text-gray-900">
                        <a href={`/help/${searchRes.link}`} className="relative hover:underline">
                          {searchRes.title}
                        </a>
                      </p>
                    </div>
                    <p className="mt-1 line-clamp-2 text-sm/6 text-gray-600">
                      <a href={`/help/${searchRes.link}`} className="relative hover:underline">
                        {searchRes.description}
                      </a>
                    </p>
                  </div>
                </li>
              ))}
            </ul>
          )}    
        </div>
      </div>
    </>
  )
}
