import { PostBlocksTable } from "@/tina/__generated__/types"

export function Table( props: PostBlocksTable ) {
  return (
    <div className="prose mb-[10px] max-w-none">
      <table className="table-auto">
        <thead>
          <tr>
            <td colSpan={props.headers?.length} className="text-center">
              <h4 className="text-[#8c0c04]">{props.title}</h4>
            </td>
          </tr>
          <tr>
            {props.headers?.map( ( header, i ) => (
              <th>{header?.title}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {props.values?.map( ( row, i ) => (
            <tr key={i}>
              <td>{row?.col1}</td>
              <td>{row?.col2}</td>
              <td>{row?.col3}</td>
              <td>{row?.col4}</td>
              {row?.col5 !== "" ? <td>{row?.col5}</td> : null}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
