import { Url } from "next/dist/shared/lib/router/router"
import Link from "next/link"
import { PostBlocksButton } from "@/tina/__generated__/types"

import { Button } from "@/components/ui/button"

export function ButtonBlock( props: PostBlocksButton ) {
  return (
    <div
      className={`flex ${props.centered ? `justify-center` : `justify-start`}`}
    >
      <Button asChild className="bg-[#8c0c04]">
        <Link className="uppercase no-underline" href={props.link as Url}>
          {props.text}
        </Link>
      </Button>
    </div>
  )
}
