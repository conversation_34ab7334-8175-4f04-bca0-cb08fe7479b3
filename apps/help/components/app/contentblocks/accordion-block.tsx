import { PostBlocksAccordionContent } from "@/tina/__generated__/types"
import { TinaMarkdown } from "tinacms/dist/rich-text"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion"
import { components } from "@/components/app/page/components"

export function AccordionBlock( props: PostBlocksAccordionContent ) {
  return (
    <div className="mt-[10px] w-full">
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger className="bg-slate-50 px-[10px] font-serif">
            {props.text}
          </AccordionTrigger>
          <AccordionContent className="prose mt-[10px] max-w-none px-[10px]">
            <TinaMarkdown content={props.richtext} components={components} />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  )
}
