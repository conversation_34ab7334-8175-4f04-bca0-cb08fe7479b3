import { PostBlocksTabsContent } from "@/tina/__generated__/types"
import { TinaMarkdown } from "tinacms/dist/rich-text"

import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { components } from "@/components/app/page/components"

export function TabBlock( props: PostBlocksTabsContent ) {
  return (
    <div className="prose mt-[10px] max-w-none">
      {props.Tab && props.Tab.length > 0 && (
        <Tabs defaultValue={props.Tab[0]?.text as string}>
          <TabsList>
            {props.Tab.map( ( tab, i ) => (
              <TabsTrigger value={tab?.text as string}>{tab?.text}</TabsTrigger>
            ))}
          </TabsList>
          {props.Tab.map( ( tab, i ) => (
            <TabsContent value={tab?.text as string}>
              <Card>
                <CardContent>
                  <TinaMarkdown
                    content={tab?.richtext}
                    components={components}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  )
}
