import { Url } from "next/dist/shared/lib/router/router"
import Image from "next/image"
import Link from "next/link"
import { PostBlocksImageLinkBlocks } from "@/tina/__generated__/types"
import { ChevronRight } from "lucide-react"

import { Button } from "@/components/ui/button"

export function ImageLinkBlock( props: PostBlocksImageLinkBlocks ) {
  return (
    <div className="mb-[20px] max-w-none">
      <div className="prose mb-[10px]">
        <h2>{props.title}</h2>
      </div>
      {props.cards && props.cards.length > 0 && (
        <div className={`grid gap-4 sm:grid-cols-2`}>
          {props.cards.map( ( card ) => (
            <div>
              {card?.link && card?.image && (
                <Link href={card?.link as Url}>
                  <Image
                    src={card?.image as string}
                    alt={card?.title as string}
                    style={{
                      width: "100%",
                      height: "auto",
                    }}
                    width={500}
                    height={300}
                  />
                </Link>
              )}
              <div className="my-[10px] text-sm">{card?.description}</div>
              {card?.link && (
                <Button asChild variant="secondary">
                  <Link href={card?.link as Url}>
                    Learn more <ChevronRight />
                  </Link>
                </Button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
