import Image from "next/image"
import { PostBlocksImageSections } from "@/tina/__generated__/types"

export function ImageSectionBlock( props: PostBlocksImageSections ) {
  return (
    <div className="max-w-none">
      <div className="prose mb-[10px]">
        <h2>{props.title}</h2>
        <p>{props.description}</p>
      </div>
      {props.cards && props.cards.length > 0 && (
        <div className={`grid gap-4 sm:grid-cols-3`}>
          {props.cards.map((card) => (
            <div className="prose mb-[10px]">
              <h3>{card?.title}</h3>
              {card?.image && (
                <Image
                  src={card?.image as string}
                  alt={card?.title as string}
                  style={{
                    width: "100%",
                    height: "auto",
                  }}
                  width={500}
                  height={300}
                />
              )}
              <p className="text-sm">{card?.description}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
