import { Url } from "next/dist/shared/lib/router/router"
import Link from "next/link"
import Vimeo from "@u-wave/react-vimeo"
import { 
  AlertCircle, 
  DollarSign, 
  HelpCircle, 
  Info 
} from "lucide-react"
import { TinaMarkdown, type Tina<PERSON><PERSON>downContent } from "tinacms/dist/rich-text"

import { 
  Alert, 
  AlertDescription, 
  AlertTitle 
} from "@/components/ui/alert"
import { Button } from "@/components/ui/button"

export const components = {
  Vimeo: ( props: { id: string }) => {
    return (
      <div className="sm:w-full">
        <Vimeo video={props.id} responsive />
      </div>
    )
  },
  Button: ({
    text = "",
    link = "",
    centered = false,
    target = "_self"
  }: {
    text: string
    link: string
    centered: boolean
    target: "_blank" | "_self" | "_parent" | "_top"
  }) => {
    return (
      <div className={`flex ${centered ? `justify-center` : `justify-start`}`}>
        {link ? (
          <Button asChild className="bg-[#8c0c04]">
            <Link
              className="uppercase no-underline"
              href={link as Url}
              target={target}
            >
              {text}
            </Link>
          </Button>
        ) : null}
      </div>
    )
  },
  Arabic: ({ content }: { content: TinaMarkdownContent }) => {
    return (
      <div className="rtl w-full text-right font-arabic text-lg">
        <TinaMarkdown content={content} />
      </div>
    )
  },
  Alert: ({
    title = "",
    description = "",
    icon = "info"
  }: {
    title: string
    description: string
    icon: "info" | "warning" | "question" | "money"
  }) => {
    return (
      <Alert>
        {icon === "info" && <Info />}
        {icon === "warning" && <AlertCircle />}
        {icon === "question" && <HelpCircle />}
        {icon === "money" && <DollarSign />}
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{description}</AlertDescription>
      </Alert>
    )
  },
  Wufoo: ({
    title = "Wufoo Form",
    height,
    link
  }: {
    title: string
    height: number
    link: string
  }) => {
    return (
      <iframe
        height={height}
        title={title}
        style={{ width: "100%", border: "none" }}
        sandbox="allow-popups-to-escape-sandbox allow-top-navigation allow-scripts allow-popups allow-forms allow-same-origin"
        src={link}
      >
        <a href={link}>{`Please complete ${title}`}</a>{" "}
      </iframe>
    )
  }
}
