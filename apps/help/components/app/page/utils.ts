import { Class_TermsTerms, PostAndNavQuery } from "@/tina/__generated__/types"

// export function matchNav( nav: PostAndNavQuery["nav"], path: string ) {
//   for ( const link of nav.links! ) {
//     if ( trimPath( link?.link as string ) === path ) {
//       return { nav: link, index: 0 }
//     }
//     if ( link?.sublinks && link.sublinks.length > 0 ) {
//       let i = 0
//       for ( const sublink of link.sublinks ) {
//         if ( trimPath( sublink?.link as string ) === path ) {
//           return { nav: link, index: i }
//         }
//         i++
//       }
//     }
//   }
// }

export function trimPath( path: string | string[] ) {
  const joinedPath = Array.isArray(path) ? path.join( "/" ) : path
  const trimmedPath = joinedPath.replace( /^\/+/, "" )
  const finalPath = trimmedPath.replace( /\/+$/, "" )
  return finalPath
}

export function DateConverter( date: string | undefined ) {
  // Assuming date is a valid Date object or a string representing a date
  const formattedDate = new Date(date as string).toLocaleDateString( "en-US", {
    year: "numeric",
    month: "short",
    day: "2-digit"
  })

  return formattedDate
}

export function sortTerms( a: Class_TermsTerms | null, b: Class_TermsTerms | null ) {
  if ( a?.orientation_start && b?.orientation_start ) {
    if ( a?.orientation_start < b?.orientation_start ) {
      return -1
    }
    if ( a.orientation_start > b.orientation_start ) {
      return 1
    }
  }
  return 0
}
