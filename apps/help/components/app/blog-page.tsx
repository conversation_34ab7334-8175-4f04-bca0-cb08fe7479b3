"use client"

import Image from "next/image"
import { PostAndNavQuery } from "@/tina/__generated__/types"
import { tina<PERSON><PERSON>, useTina } from "tinacms/dist/react"
import { TinaMarkdown } from "tinacms/dist/rich-text"

import { sidebarSection, sidebarLinks } from "@/lib/utils"

import { Footer } from "@/components/footer"
import { SiteHeader } from "@/components/site-header"

import { AccordionBlock } from "@/components/app/contentblocks/accordion-block"
import { ButtonBlock } from "@/components/app/contentblocks/button-block"
import { ImageLinkBlock } from "@/components/app/contentblocks/image-link-block"
import { ImageSectionBlock } from "@/components/app/contentblocks/image-section-block"
import { RichTextBlock } from "@/components/app/contentblocks/richtext-block"
import { TabBlock } from "@/components/app/contentblocks/tab-block"
import { Table } from "@/components/app/contentblocks/table"
import { VimeoBlock } from "@/components/app/contentblocks/vimeo-block"

export function BlogPageComponent( props: {
  data: PostAndNavQuery
  variables: { relativePath: string }
  query: string
}) {
  const { data } = useTina( props )
  const backgroundImage = ( data.post.image ) ? `${data.post.image}` : "none"
  const sidebar = data.post.sidebars

  const parseLinks = ( links: any ) => {
    if ( links.__typename === "SidebarsBlocksSection" ) {
      return (
        <div className="mt-2 font-semibold text-muted">{links.title}</div>
      )
    }

    if ( links.__typename === "SidebarsBlocksLinks" ) {
      const htmlElement = sidebarLinks( links )
      return (
        <a className="text-muted" href={htmlElement.link}>{htmlElement.title}</a>
      )
    }
  }

  return (
    <>
      <SiteHeader header={data.header} nav={data.nav} />
      <div className="flex min-h-[calc(100vh-65px)] flex-col">
        {backgroundImage !== "none" && (
          <section className={`relative h-[35vh]`}>
            <Image
              alt={data.post.title || ""}
              className={`size-full object-cover`}
              height={1080}
              src={backgroundImage}
              style={{
                aspectRatio: "1920/1080",
                objectFit: "cover"
              }}
              data-tina-field={tinaField( data.post, "image" )}
              width={1920}
            />
          </section>
        )}

        <section className="container mx-auto grow px-4 py-6">
          <main>
            <div className="mx-auto grid max-w-2xl grid-cols-1 grid-rows-1 items-start gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="-mx-4 px-4 sm:mx-0 sm:rounded-lg sm:px-8 sm:pb-14 lg:col-span-2 lg:row-span-2 lg:row-end-2 xl:px-16">
                <h1 className="pb-5 pt-6 text-left font-semibold text-muted sm:text-2xl md:text-4xl">
                  {data.post?.title || ""}
                </h1>
                <div className="prose max-w-none" data-tina-field={tinaField( data.post, "body" )}>
                  <TinaMarkdown content={data.post.body} />
                </div>
                <div>
                  {data.post.blocks?.map( ( block, i ) => {
                    switch ( block?.__typename ) {
                      case "PostBlocksRichText": {
                        return <RichTextBlock key={i} {...block} />
                      }
                      case "PostBlocksTable": {
                        return <Table key={i} {...block} />
                      }
                      case "PostBlocksTabsContent": {
                        return <TabBlock key={i} {...block} />
                      }
                      case "PostBlocksAccordionContent": {
                        return <AccordionBlock key={i} {...block} />
                      }
                      case "PostBlocksImageLinkBlocks": {
                        return <ImageLinkBlock key={i} {...block} />
                      }
                      case "PostBlocksImageSections": {
                        return <ImageSectionBlock key={i} {...block} />
                      }
                      case "PostBlocksButton": {
                        return <ButtonBlock key={i} {...block} />
                      }
                      case "PostBlocksVimeo": {
                        return <VimeoBlock key={i} {...block} />
                      }
                    }
                  })}
                </div>
              </div>

              {/* Sidebar */}
              <div className="mt-6 rounded-lg bg-accent py-6 text-gray-500 shadow-sm lg:col-start-3 lg:row-end-1">
                <dl className="flex flex-wrap">
                  <div className="flex w-full flex-none gap-x-4 px-6">
                    <h2 className="font-semibold text-primary">
                      {sidebar && (
                        <>{sidebarSection( sidebar._sys.filename )}</>
                      )}
                    </h2>
                  </div>
                  <div className="mt-2 grid w-full grid-cols-1 gap-x-4 px-6">
                    {sidebar && sidebar.blocks && sidebar.blocks.map( ( block: any ) => (
                      <div className="mb-1.5 block">{parseLinks( block )}</div>
                    ) )}
                  </div>
                </dl>
              </div>
            </div>
          </main>
        </section>

        <Footer footer={data.footer} />
      </div>
    </>
  )
}
