import { LucideProps, Moon, Sun, Twitter, MapPin, Facebook, Youtube } from "lucide-react"

import BallStar from "./ball-star"
import Broadcast from "./broadcast"
import ChatApp from "./chat-app"
import ChatBubbles from "./chat-bubbles"
import ChatWindow from "./chat-window"
import CloudCommunication from "./cloud-communication"
import GlobalChat from "./global-chat"
import GlobalTalk from "./global-talk"
import GroupChat from "./group-chat"
import HomeInternet from "./home-internet"
import MediaShare from "./media-share"
import NewMessage from "./new-message"
import PencilChat from "./pencil-chat"
import ProfilePicture from "./profile-picture"
import ProfilePictureSocial from "./profile-picture-social"
import ProfileSearch from "./profile-search"
import RomanticVideoChat from "./romantic-video-chat"
import SendingEmail from "./sending-email"
import ShareFile from "./share-file"
import SocialEmoji from "./social-emoji"
import StarWindow from "./star-window"
import Thumbsup from "./thumbs-up"
import VideoCall from "./video-call"
import VideoWindows from "./video-windows"

export const IconList = [
  BallStar,
  Broadcast,
  ChatApp,
  ChatBubbles,
  ChatWindow,
  CloudCommunication,
  GlobalChat,
  GlobalTalk,
  GroupChat,
  HomeInternet,
  MediaShare,
  NewMessage,
  PencilChat,
  ProfilePictureSocial,
  ProfilePicture,
  ProfileSearch,
  RomanticVideoChat,
  SendingEmail,
  ShareFile,
  SocialEmoji,
  StarWindow,
  Thumbsup,
  VideoCall,
  VideoWindows,
]

export const FeaturedIcons = {
  BallStar,
  Broadcast,
  ChatApp,
  ChatBubbles,
  ChatWindow,
  CloudCommunication,
  GlobalChat,
  GlobalTalk,
  GroupChat,
  HomeInternet,
  MediaShare,
  NewMessage,
  PencilChat,
  ProfilePictureSocial,
  ProfilePicture,
  ProfileSearch,
  RomanticVideoChat,
  SendingEmail,
  ShareFile,
  SocialEmoji,
  StarWindow,
  Thumbsup,
  VideoCall,
  VideoWindows,
}

export const BasicIcons = {
  sun: Sun,
  moon: Moon,
  googlemaps: MapPin,
  twitter: Twitter,
  facebook: Facebook,
  youtube: Youtube
}

export const Logo = ( props: LucideProps ) => (
  <svg 
    version="1.1" 
    viewBox="0 0 2048 2048" 
    width="192" 
    height="192" 
    xmlns="http://www.w3.org/2000/svg"
    aria-labelledby="title desc"
    className={`h-auto w-10 ${props.className}`}
  >
    <path 
      transform="translate(908,194)" 
      d="m0 0h52l35 3 33 5 37 7 40 10 36 12 30 11 30 13 25 12 22 12 22 13 16 10 24 16 10 8 14 10 16 13 11 9 13 12 8 7 8 8 2 1v2l4 2 14 14 7 8 8 8 7 8 9 10 11 14 14 18 14 20 10 15 13 21 15 26 18 36 12 28 11 30 13 40 8 32 6 31 7 49 3 30 1 17v77l-2 30-4 33-6 38-7 33-7 27-17 52-13 32-17 36-15 28-14 23-12 19-13 18-10 14-13 16-9 11-13 15-18 20-19 19-8 7-13 12-11 9-28 22-13 10-15 10-20 13-21 13-24 13-33 17-24 10-31 12-33 11-44 11-37 7-51 7-11 2 17 7 34 10 52 18 42 15 26 10 40 15 30 11 34 13 28 10 37 13 36 12 43 12 47 10 25 4 22 2h46l31-4 27-7 26-9 24-11 15-9 15-10 12-9 10-9 9-8 7-8 9-12 16-24 11-20 10-16 8-8 6 1 11 7 9 8 1 2v9l-7 25-8 21-9 19-11 18-8 12-10 13-13 15-11 12-13 12-14 11-8 7-17 12-27 16-23 12-27 11-36 12-24 7-34 7-55 9-32 3-14 1h-36l-36-3-60-10-28-6-36-10-38-12-26-9-30-12-26-11-30-13-35-16-28-13-36-17-36-16-30-13-28-11-37-13-30-9-29-7-21-3-23-2-21-1h-18l-30 2-23 4-17 5-21 9-20 11-16 12-16 15-9 10-11 14-9 12-20 30-9 12-7 8-10 4-9-3-6-4-4-5-3-6-1-4v-11l4-18 5-14 8-16 10-17 7-10 9-12 9-11 11-12h2l2-4 17-16 17-13 23-15 15-9 23-12 21-9 24-9 24-7 29-6 61-8h2v-2l-5-2-12-5-23-8-25-9-29-13-34-17-23-13-26-16-16-11-17-12-15-12-14-11-14-12-10-9-8-7-17-16-15-16-11-12-9-11-11-13-10-13-12-16-16-23-13-21-15-26-14-27-15-33-13-34-12-38-11-41-9-44-8-47-3-17-2-28-1-15v-20l2-34 8-52 12-60 11-41 10-33 11-30 17-39 10-21 10-18 8-14 12-20 8-12 11-16 11-15 10-13 11-14 12-14 12-13 43-43 8-7 13-11 14-11 18-14 17-12 21-14 19-12 26-15 25-13 26-12 25-10 24-9 38-12 37-9 26-5 37-6 28-3zm12 48-32 2-31 4-36 8-32 10-32 13-25 13-20 12-14 10-16 12-16 13-15 14-15 15-7 8-9 11-8 10-13 18-8 12-7 11-11 18-12 22-14 31-14 35-11 34-11 40-9 39-6 36-6 44-4 45-2 35-2 64 1 53 3 50 4 43 6 46 6 37 10 43 14 48 10 29 11 26 9 20 12 25 14 24 14 22 13 18 9 11 12 14 12 12 1 2h2v2l5 4 1 2h2v2l8 7 13 12 11 9 19 14 12 7 13 8 14 8 22 10 16 6 19 7 31 9 27 6 38 4 17 1h42l39-3 25-4 35-9 34-12 24-10 19-10 27-16 20-14 11-9 15-13 34-34 9-11 7-8 12-16 10-15 12-19 15-28 11-22 10-23 9-24 12-37 11-41 10-50 7-46 4-33 4-52 2-43v-70l-3-63-3-37-8-61-10-54-7-30-9-31-14-41-11-27-11-24-15-29-14-23-16-23-10-13-9-11-11-12-7-8-17-17-11-9-12-10-19-14-15-10-26-15-22-11-26-10-21-7-26-7-33-6-22-3-29-2z" 
      fill="#8C0C04"
    />
  </svg>
)
