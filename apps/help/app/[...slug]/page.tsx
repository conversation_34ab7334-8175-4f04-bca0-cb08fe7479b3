import { Suspense } from "react"
import type { Metada<PERSON> } from "next"
import { notFound } from "next/navigation"
import client from "@/tina/__generated__/client"

import { PageComponent } from "@/components/app/page"

type Props = { params: Promise<{ slug: string[] }> }

// Function to check if a slug should be ignored
function shouldIgnoreSlug( slug: string[] ): boolean {
  const slugString = slug.join( "/" )

  // Ignore well-known paths
  if ( slugString.startsWith( ".well-known/" ) ) return true

  // Ignore common system/browser requests
  const ignoredPaths = [
    "favicon.ico",
    "robots.txt",
    "sitemap.xml",
    "manifest.json",
    "_next",
    "api",
    ".env",
    "admin",
    ".git",
    "node_modules",
    "help", // Don't let [...slug] handle help routes
  ]

  return ignoredPaths.some( ( path ) => slugString.startsWith( path ) )
}

export async function generateMetadata({ params }: Props ): Promise<Metadata> {
  const { slug } = await params

  // Return minimal metadata for ignored paths
  if ( shouldIgnoreSlug( slug ) ) {
    return {
      title: "Not Found",
      robots: { index: false, follow: false }
    }
  }

  try {
    const headerQuery = await client.queries.headerConnection()
    const headerData = headerQuery.data.headerConnection.edges
      ? headerQuery.data.headerConnection.edges[0]?.node
      : undefined

    const slugString = Array.isArray(slug) ? slug.join( "/" ) : slug
    const currentUrl = process.env.NEXT_PUBLIC_HELP_URL + "/" + slugString
    const title = headerData?.siteTitle || "Knowledge Base"
    const description = headerData?.siteDescription || ""
    const image = headerData?.logo || ""

    return {
      title: title,
      description: description,
      metadataBase: new URL( process.env.NEXT_PUBLIC_HELP_URL || "http://localhost:3000" ),
      openGraph: {
        title,
        description,
        url: currentUrl,
        siteName: "Qasid Arabic Institute | Knowledge Base",
        images: image ? [{ url: image }] : [],
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: image ? [{ url: image }] : [],
      },
    }
  } catch ( error ) {
    console.warn( "Failed to fetch header data for metadata, using defaults" )
    return {
      title: "Knowledge Base",
      description: "Qasid Arabic Institute | Knowledge Base",
      metadataBase: new URL( process.env.NEXT_PUBLIC_HELP_URL || "http://localhost:3000" ),
    }
  }
}

export default async function Page({ params }: Props ) {
  const { slug } = await params

  if ( shouldIgnoreSlug( slug ) ) { // Return 404 for ignored paths
    notFound()
  }

  const slugString = Array.isArray( slug ) ? slug.join( "/" ) : slug
  try {
    const result = await client.queries.pageAndNav({ relativePath: `${slugString}.md` })

    return (
      <Suspense>
        <PageComponent {...result} />
      </Suspense>
    )
  } catch ( error ) {
    console.error( `Failed to fetch page data for slug: ${slugString}`, error )
    notFound()
  }
}

export async function generateStaticParams() {
  try {
    // Always try to generate static params, even in production
    const pages = await client.queries.pageConnection()
    const paths =
      pages.data?.pageConnection.edges
        ?.map( ( edge ) => {
          const breadcrumbs = edge?.node?._sys.breadcrumbs
          const slug = Array.isArray( breadcrumbs ) ? breadcrumbs : [breadcrumbs].filter( Boolean )

          // Filter out any paths that should be ignored
          if ( shouldIgnoreSlug( slug as any ) ) return null

          return { slug }
        }).filter( Boolean ) || []

    console.log( `Generated ${paths.length} static params for [...slug]` )
    return paths
  } catch ( error: any ) {
    console.warn( "Failed to generate static params for [...slug]: ", error.message )

    // Return some basic static params as fallback
    return [
      { slug: ["online"] }, 
      { slug: ["contact"] }, 
      { slug: ["amman"] }
    ]
  }
}
