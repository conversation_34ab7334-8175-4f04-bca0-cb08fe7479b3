import { Suspense } from "react"
import type { <PERSON>ada<PERSON> } from "next"
import client from "@/tina/__generated__/client"
import { PageComponent } from "@/components/app/page"

export async function generateMetadata(): Promise<Metadata> {
  try {
    const headerQuery = await client.queries.headerConnection()
    const headerData = headerQuery.data.headerConnection.edges
      ? headerQuery.data.headerConnection.edges[0]?.node
      : undefined

    const currentUrl = process.env.NEXT_PUBLIC_HELP_URL + "/"
    const title = headerData?.siteTitle || "Knowledge Base"
    const description = headerData?.siteDescription || "Qasid Arabic Institute Knowledge Base"
    const image = headerData?.logo || ""

    return {
      title: title,
      description: description,
      metadataBase: new URL( process.env.NEXT_PUBLIC_HELP_URL || "http://localhost:3000" ),
      openGraph: {
        title,
        description,
        url: currentUrl,
        siteName: "Qasid Arabic Institute | Knowledge Base",
        images: image ? [{ url: image }] : [],
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: image ? [{ url: image }] : [],
      },
    }
  } catch ( error ) {
    console.warn( "Failed to fetch header data for home page metadata, using defaults" )
    return {
      title: "Knowledge Base",
      description: "Qasid Arabic Institute | Knowledge Base",
      metadataBase: new URL( process.env.NEXT_PUBLIC_HELP_URL || "http://localhost:3000" )
    }
  }
}

export default async function Page() {
  try {
    const result = await client.queries.pageAndNav({ relativePath: `home.md` })

    return (
      <Suspense>
        <PageComponent {...result} />
      </Suspense>
    )
  } catch ( error ) {
    console.error( "Failed to fetch home page data: ", error )
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Welcome</h1>
        <p>to the Qasid Knowledge Base</p>
      </div>
    )
  }
}
