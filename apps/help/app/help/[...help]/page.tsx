import type { Metadata } from "next"
import { notFound } from "next/navigation"
import client from "@/tina/__generated__/client"
import { BlogPageComponent } from "@/components/app/blog-page"

type Props = { params: Promise<{ help: string[] }> }

// Function to check if a help slug should be ignored
function shouldIgnoreHelpSlug( help: string[] ): boolean {
  const helpString = help.join( "/" )
  // Ignore well-known paths and system files
  const ignoredPaths = [
    ".well-known", 
    "favicon.ico", 
    "robots.txt", 
    "sitemap.xml", 
    "manifest.json", 
    "_next", 
    "api"
  ]

  return ignoredPaths.some( ( path ) => helpString.startsWith( path ) )
}

export async function generateMetadata({ params }: Props ): Promise<Metadata> {
  const { help } = await params

  // Return minimal metadata for ignored paths
  if ( shouldIgnoreHelpSlug( help ) ) {
    return {
      title: "Not Found",
      robots: { index: false, follow: false }
    }
  }

  try {
    const headerQuery = await client.queries.headerConnection()
    const headerData = headerQuery.data.headerConnection.edges
      ? headerQuery.data.headerConnection.edges[0]?.node
      : undefined

    const helpString = Array.isArray(help) ? help.join( "/" ) : help
    const currentUrl = process.env.NEXT_PUBLIC_HELP_URL + "/help/" + helpString
    const title = headerData?.siteTitle || "Knowledge Base"
    const description = headerData?.siteDescription || ""
    const image = headerData?.logo || ""

    return {
      title: title,
      description: description,
      metadataBase: new URL( process.env.NEXT_PUBLIC_HELP_URL || "http://localhost:3000" ),
      openGraph: {
        title,
        description,
        url: currentUrl,
        siteName: "Qasid Arabic Institute | Knowledge Base",
        images: image ? [{ url: image }] : [],
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: image ? [{ url: image }] : [],
      },
    }
  } catch ( error ) {
    console.warn( "Failed to fetch header data for metadata, using defaults" )
    return {
      title: "Knowledge Base",
      description: "Qasid Arabic Institute | Knowledge Base",
      metadataBase: new URL( process.env.NEXT_PUBLIC_HELP_URL || "http://localhost:3000" ),
    }
  }
}

export default async function Page({ params }: Props ) {
  const { help } = await params

  if ( shouldIgnoreHelpSlug( help ) ) { // Return 404 for ignored paths
    notFound()
  }
  const helpString = Array.isArray( help ) ? help.join( "/" ) : help

  try {
    const result = await client.queries.postAndNav({ relativePath: `${helpString}.mdx` })

    return <BlogPageComponent {...result} />
  } catch ( error ) {
    console.error( `Failed to fetch post data for help: ${helpString}`, error )
    notFound()
  }
}

export async function generateStaticParams() {
  try {
    // Always try to generate static params
    const posts = await client.queries.postConnection()
    const paths =
      posts.data?.postConnection.edges
        ?.map( ( edge ) => {
          const breadcrumbs = edge?.node?._sys.breadcrumbs
          const help = Array.isArray(breadcrumbs) ? breadcrumbs : [breadcrumbs].filter(Boolean)

          // Filter out any paths that should be ignored
          if ( shouldIgnoreHelpSlug( help as any ) ) return null

          return { help }
        }).filter( Boolean ) || []

    console.log( `Generated ${paths.length} static params for [...help]` )
    return paths
  } catch (error: any) {
    console.warn( "Failed to generate static params for [...help]: ", error.message )

    return [ // Return some basic static params as fallback
      { help: ["request-a-transcript"] }, 
      { help: ["online-placement"] }, 
      { help: ["student-portal"] }
    ]
  }
}
