import type { <PERSON><PERSON><PERSON> } from "next"
import Head from "next/head"
import { Analytics } from "@vercel/analytics/react"
import { ThemeProvider } from "@/components/theme-provider"
import {
  Figtree,
  Merriweather,
  <PERSON><PERSON><PERSON>,
  Noto_Sans_Arabic
} from "next/font/google"
import { cn } from "@/lib/utils"

import "@/styles/globals.css"
import "@/styles/styles.css"

const monserrat = Montserrat({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-monserrat"
})

const merriweather = Merriweather({
  subsets: ["latin"],
  weight: "400",
  style: ["normal", "italic"],
  variable: "--font-serif"
})

const figtree = Figtree({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-sans"
})

const arabic = Noto_Sans_Arabic({
  subsets: ["arabic"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-arabic"
})

interface RootLayoutProps { children: React.ReactNode }
export const metadata: Metadata = {
  title: "Qasid Arabic Institute | Knowledge Base",
  description: "Qasid Arabic Institute offers immersive Arabic courses online and in Jordan.",
  icons: {
    icon: "/icon.png"
  },
  openGraph: {
    title: "Qasid Arabic Institute | Knowledge Base",
    description: "Qasid Arabic Institute offers immersive Arabic courses online and in Jordan.",
    url:  process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_HELP_URL || "https://help.qasid.com",
    siteName: "Qasid Institute | Knowledge Base",
    images: [{ url: "/images/wp-content-uploads-2015-10-qasid_bridge_wadi_rum.jpg" }]
  },
  twitter: {
    card: "summary_large_image",
    title: "Qasid Arabic  Institute | Knowledge Base",
    description: "Qasid Arabic Institute offers immersive Arabic courses online and in Jordan.",
    images: [{ url: "/images/wp-content-uploads-2015-10-qasid_bridge_wadi_rum.jpg" }]
  }
}

export default function RootLayout({ children }: RootLayoutProps ) {
  return (
    <html lang="en" suppressHydrationWarning>
      <Head>
        <meta charSet="utf-8" />
        <meta name="robots" content="all" />
        <meta property="og:logo" content="/<EMAIL>" />
        <meta property="og:type" content="website" />
        <link rel="icon" href="/icon.ico" sizes="any" />
      </Head>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          merriweather.variable,
          monserrat.variable,
          figtree.variable,
          arabic.variable
        )}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
        <Analytics/>
      </body>
    </html>
  )
}
