import type { MetadataRoute } from "next"
import client from "@/tina/__generated__/client"

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_HELP_URL || "https://help.qasid.com"

// Required for static export
export const dynamic = "force-static"

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseRoutes: MetadataRoute.Sitemap = [
    {
      url: SITE_URL,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 1
    }
  ]

  try {
    // Get all posts (help articles)
    const postsQuery = await client.queries.postConnection()
    const posts = postsQuery.data?.postConnection.edges || []

    const postRoutes: MetadataRoute.Sitemap = posts.map( ( edge ) => {
      const post = edge?.node
      const slug = post?._sys.breadcrumbs.join( "/" ) || ""

      return {
        url: `${SITE_URL}/help/${slug}`,
        lastModified: post?.published ? new Date( post.published ) : new Date(),
        changeFrequency: "weekly" as const,
        priority: 0.7
      }
    })

    // Get all pages
    const pagesQuery = await client.queries.pageConnection()
    const pages = pagesQuery.data?.pageConnection.edges || []

    const pageRoutes: MetadataRoute.Sitemap = pages
      .filter( ( edge ) => {
        const slug = edge?.node?._sys.breadcrumbs.join( "/" ) || ""
        // Filter out home page (already included in baseRoutes)
        return slug !== "home"
      })
      .map( ( edge ) => {
        const page = edge?.node
        const slug = page?._sys.breadcrumbs.join( "/" ) || ""

        return {
          url: `${SITE_URL}/${slug}`,
          lastModified: new Date(),
          changeFrequency: "monthly" as const,
          priority: 0.8
        }
      })

    console.log( `Generated sitemap with ${baseRoutes.length + postRoutes.length + pageRoutes.length} URLs` )
    return [
      ...baseRoutes, 
      ...postRoutes, 
      ...pageRoutes
    ]
  } catch ( error ) {
    console.warn( "Failed to generate sitemap from TinaCMS, using base routes only: ", error )
    return baseRoutes
  }
}
