import type { MetadataRoute } from "next"

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || "https://help.qasid.com"
export const dynamic = "force-static"

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: "*",
      allow: "/",
      disallow: [
        "/admin/", 
        "/api/", 
        "/_next/", 
        "/tina/", 
        "/.well-known/"
      ]
    },
    sitemap: `${SITE_URL}/sitemap.xml`
  }
}
