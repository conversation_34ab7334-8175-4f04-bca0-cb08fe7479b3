---
published: '2024-11-05T00:00:00.000Z'
redirect_from:
  - 'https://help.qasid.com/article/127-enabling-arabic-keyboard-on-windows'
title: Enabling Arabic Keyboard on Windows
author: content/authors/qasid.md
sidebars: content/sidebars/online.md
description: Change keyboard language in Windows 7 and Vista...
fullwidth: true
---

## Change keyboard language in Windows 7 and Vista

It is fairly simple to add or change a keyboard language in Windows 7 or Vista.

In Windows 7, enter “keyboard language” in the Start menu search box, and select “Change keyboards or other input methods”.

![](/images/sshot20100308185010.png)

In Windows Vista, open Control Panel and enter “input language” in the search box and select “Change keyboards or other input methods”. This also works in Windows 7.![](/images/sshot108.jpeg)

Now, click "Change Keyboards" to add another keyboard language or change your default language.

![](/images/image54.jpeg)

Our default input language is US English, and the default keyboard layout is the US layout.

Click "Add" to insert another input language while keeping your default input language installed.

![](/images/image55.jpeg)

Here we selected the standard Thai keyboard language (Thai Kedmanee), but you can select any language you want.

Windows offers almost any language you can imagine, so just look for the language you want, select it, and click Ok.

![](/images/image56.jpeg)

Alternatively, if you prefer, you can click "Preview" to see your layout choice before accepting it.

These are only the default characters, not the ones activated with Shift or other keys (many Asian languages use more characters than English and require the use of Shift and other keys to access them all).

Once you're finished previewing, click "Close" and then press "OK" on the previous dialog.

![](/images/image57.jpeg)

Now you will see both of your keyboard languages in the Installed services box.

You can click "Add" to select more languages, adjust the priority by moving your selected language up or down, or simply click "Apply" to add the new language.

![](/images/image58.jpeg)

Also, you can now change the default input language from the top menu.

This is the language that your keyboard will start with when you boot your computer.

So, if you mainly use English but also use another language, usually it is best to leave English as your default input language.

![](/images/image59.jpeg)

Once you’ve pressed Apply or Ok, you will see a new icon beside your system tray with the initials of your default input language.

![](/images/image60.jpeg)

If you click it, you can switch between input languages.

Alternately you can switch input languages by pressing Alt+Shift on your keyboard.

![](/images/image61.jpeg)

Some complex languages, such as Chinese, may have extra buttons to change input modes to accommodate their large alphabet.

![](/images/image62.jpeg)

If you would like to change the keyboard shortcut for changing languages, go back to the Input Languages dialog, and select the “Advanced Key Settings” tab.

Here you can change settings for Caps Lock and change or add key sequences to change between languages.

![](/images/image63.jpeg)

Also, the On-Screen keyboard will display the correct keyboard language (here the keyboard is displaying Thai), which can be a helpful reference if your physical keyboard doesn’t have your preferred input language printed on it.

To open this, simply enter “On-Screen keyboard” in the start menu search, or click *All Programs Accessories* On-Screen keyboard.

![](/images/image64.jpeg)
