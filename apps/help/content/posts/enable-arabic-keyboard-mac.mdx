---
published: '2024-11-05T00:00:00.000Z'
redirect_from:
  - 'https://help.qasid.com/article/128-enabling-arabic-keyboard-on-mac'
fullwidth: true
_template: contentPage
title: Enabling Arabic Keyboard on Mac
author: content/authors/qasid.md
sidebars: content/sidebars/online.md
description: How to enable the Arabic keyboard on Mac...
---

## How to enable the Arabic keyboard on Mac

All OS X versions are able to display Arabic perfectly fine. However, if you wish to *type* in Arabic you must enable an Arabic keyboard layout.

Doing so is simple:

* Go to System Preferences... from the Apple  menu in the top left corner.

Then:

\*\* If using OS X >= 10.9: click on Keyboard
\*\* If using OS X \<= 10.9: click on Language Text

* Click on the Input Sources tab.
* In the left sidebar, locate the "Arabic" entry and select it. Or for OS X >= 10.9, click on the + icon, locate the "Arabic" entry and add it.
* Now, whenever you need to type Arabic, you'll be able to select the Arabic keyboard from the menu bar. English is denoted by either the U.S flag or similar. Arabic is denoted by the green crescent or isolated ع depending on your OS X version.

Use a shortcut to switch between languages:

* In OS X >= 10.9: go to System Preferences - Keyboard Shortcuts. On the left sidebar, click on Input Source.

In OS X \<= 10.9: go to System Preferences -> Language Text -> Input Sources. Click on the Keyboard Shortcuts... button.

* Here you can define a shortcut for switching to the previous/next input source language. We like to use the default ⌘Space for this purpose.
* Enabling Automatically switch to a document's input source will allow you to maintain an input language per document. So you can for example open 2 documents, write one in English and the other in Arabic, and OS X will remember the keyboard association for each.

## Using the Keyboard Viewer

If your physical Mac keyboard doesn't contain Arabic symbols, you can [purchase](http://www.amazon.com/dp/B00AVBLGZG/ref=cm_sw_r_tw_dp_rD4tub0PG0A4D) [stickers](http://www.amazon.com/dp/B000ZZ1BDA/ref=cm_sw_r_tw_dp_Lo4tub0P9BZ7X) [online](http://www.amazon.com/dp/B002PDH1EI/ref=cm_sw_r_tw_dp_3E4tub1XSZ25X) and lay [them](http://www.4keyboard.com/mac-apple-stickers-arabic-c-124_125.html) on top of your keyboard.

Alternatively, you can use Keyboard Viewer, which will you show a virtual keyboard for the currently selected input language.

## Learners of Arabic will like this

Learners of Arabic will find the "Arabic QWERTY" keyboard layout useful. What it does is transliterate Latin sounds into Arabic. So you can type "A" and it will produce the Arabic character most closely associated with that sound, which is the Arabic Alef ا. And so forth for the rest of the alphabet.

To add the "Arabic QWERTY" keyboard layout, follow the steps above for adding "Arabic", but add "Arabic QWERTY" instead.
