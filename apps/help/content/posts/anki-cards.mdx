---
published: '2024-11-05T00:00:00.000Z'
redirect_from:
  - 'http://localhost:3000/help/199-using-anki-app-for-flash-cards'
title: Using Anki App for Flash Cards
author: content/authors/qasid.md
sidebars: content/sidebars/online.md
description: >-
  Anki is a popular flash card system and Ankiapp.com provides desktop, mobile
  and web interfaces for it...
fullwidth: true
blocks:
  - id: '*********'
    _template: Vimeo
---

Anki is a popular flash card system and Ankiapp.com provides desktop, mobile and web interfaces for it. We've created zip files for our main vocabulary sets that will be sent to you upon request. Below you will find the steps on how to set it up

* Set up your Anki App account from [https://www.ankiapp.com/](https://www.ankiapp.com/)
* You can use the web app by clicking AnkiApp Web or by downloading the desktop or mobile app and creating your account there
* Then, log in at the ankiapp nexus page to import the set that you need to study [https://api.ankiapp.com/nexus/](https://api.ankiapp.com/nexus/)
* Click Import
  \*\* Import from Spreadsheet file
  \*\* Click choose file and select one of the zip files from the folder that was shared with you

Note: Avoid trying to import more than one zip file at a time. That can cause upload errors *You should also type a name for the deck that will be easy for to recognize in future*

If you now go back to AnkiApp Web or any version of the app on the desktop or mobile and sign in your account, you will see the new deck under "decks" icon (second icon from the bottom after "home").

* Click on the deck to preview it and then click Download
* Click Review to start studying the set. Most of our decks will have the audio of the word's pronunciation in Arabic.
* Below is the video explanation of the instructions above
