export type SearchObject = { 
  author: string, 
  title: string, 
  link: string, 
  description: string, 
  relativePath: string 
}
export type SearchResults = Array<SearchObject>

export type SearchPost = {
  authors?: string,
  blocks?: string | null,
  body?: any,
  description?: any,
  fullwidth?: boolean,
  hero?: string | null,
  id: string,
  image?: string | null,
  sidebars?: any,
  title: string,
  __typename: string,
  _sys: any
}