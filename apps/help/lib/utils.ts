import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge( clsx( inputs ) )
}

export const classNames = ( ...classes: any ) => {
  return classes.filter( Boolean ).join( " " );
}

export function sidebarSection( title: string ) {
  if ( title === "amman" ) return "Amman Program"
  if ( title === "online" ) return "Online Learning"
}

export const sidebarLinks = ( block: any ) => {
  let returnLinks: any = {};
  if ( block.__typename === "SidebarsBlocksLinks" ) {
    returnLinks = {
      title: block.label,
      link: block.linkedPage._sys.filename
    }
  }

  return returnLinks
}