"use server"

import { SearchPost, SearchResults } from "@/lib/types"
import postResults from "@/generated/search-results.json";

function filterPosts( posts: any, query: string ) {
  const caseInsensitive = new RegExp( query, "i" )
  let filterResults: SearchResults = []

  posts.forEach( ( post: any ) => {
    if ( post.title && ( caseInsensitive.test( post.title ) || caseInsensitive.test( post.description ) || caseInsensitive.test( post.content ) ) ) {
      filterResults.push({ 
        author: "/images/logo.png",
        title: post.title,
        link: post.breadcrumb,
        description: post.description,
        relativePath: post.relativeLink
      })
    }
  })

  return filterResults
}

export async function searchPosts( searchTerm: string ) {
  const results = filterPosts( postResults, searchTerm ) // return posts that match query param

  return results ?? []
}