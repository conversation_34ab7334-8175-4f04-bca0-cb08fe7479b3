import Link from "next/link"
import Image from "next/image"

import <PERSON><PERSON><PERSON> from "@/public/qasid/payments-header.jpg"

export default function Header() {
  return (
    <div className="relative">
      {/* Hero Image */}
      <div className="qasid-header-image">
        <Image
          src={MLogo}
          alt="Students studying at Qasid Arabic Institute"
          fill
          className="object-cover"
          priority
        />
        <div className="qasid-header-overlay" />
      </div>

      {/* Header Content */}
      <header className="absolute top-0 left-0 right-0 z-10">
        <div className="container mx-auto px-4 py-6">
          <nav className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="text-white qasid-logo">
                <img className="mb-6" width="122px" src="/qasid/qasid_logo_white.png" />
              </Link>
            </div>

            {/* Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <Link href={process.env.NEXT_PUBLIC_HOME_URL + "/admissions"} className="qasid-nav-link">
                ADMISSIONS
              </Link>
              <Link href={process.env.NEXT_PUBLIC_HOME_URL + "/admissions/tuition"} className="qasid-nav-link">
                TUITION
              </Link>
              <Link href={process.env.NEXT_PUBLIC_HOME_URL + "/admissions/online-payment"}  className="qasid-nav-link">
                PAYMENT
              </Link>
            </div>
          </nav>
        </div>
      </header>
    </div>
  )
}
