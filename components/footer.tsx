import Link from "next/link"

import { Separator } from "@/components/ui/separator"
import { GET_UPCOMING_TERMS } from "@/lib/endpoints"

const { format } = require("date-fns")

interface TermType {
  _id: string
  id: number
  title: string
  comments: string
  open_for_apply: boolean
  start_date: Date
  end_date: Date
  canvasid?: number | string
}

export default async function Footer() {
  const data = await fetch( GET_UPCOMING_TERMS )
  const { results } = await data.json()

  return (
    <footer className="bg-muted py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:grid-cols-3">
            <div>
              <Link href={process.env.NEXT_PUBLIC_HOME_URL + "/admissions"} className="footer-link">
                ADMISSIONS
              </Link>
            </div>
            <div>
              <Link href={process.env.NEXT_PUBLIC_HOME_URL + "/admissions/tuition"} className="footer-link">
                TUITION
              </Link>
            </div>
            <div>
              <Link href={process.env.NEXT_PUBLIC_HOME_URL + "/admissions/online-payment"} className="footer-link">
                PAYMENT
              </Link>
            </div>
          </div>

          <div>
            <h3 className="footer-heading">IMPORTANT LINKS</h3>
            <ul className="space-y-2">
              <li>
                <a href={process.env.NEXT_PUBLIC_HOME_URL + "/admissions/apply-now"} className="footer-link">
                  Apply Now
                </a>
              </li>
              <li>
                <a target="_blank" href="https://help.qasid.com/" className="footer-link">
                  Frequently Asked Questions
                </a>
              </li>
              <li>
                <a target="_blank" href="http://www.qasidonline.com/qasid-information-package/" className="footer-link">
                  Program Brochure
                </a>
              </li>
              <li>
                <a href={process.env.NEXT_PUBLIC_HOME_URL + "/refund-policy"} className="footer-link">
                  Refund Policy
                </a>
              </li>
              <li>
                <a href={process.env.NEXT_PUBLIC_HOME_URL + "/terms-and-conditions"} className="footer-link">
                  Terms and Conditions
                </a>
              </li>
              <li>
                <a href={process.env.NEXT_PUBLIC_HOME_URL + "/privacy-policy"} className="footer-link">
                  Privacy Policy
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="footer-heading">IMPORTANT CORE PROGRAM DATES</h3>
            <div className="footer-section">
              {Array.isArray( results ) && results.map( ( term: TermType ) => (
                <div className="footer-date">
                  <h4 className="font-semibold">{term.title?.toUpperCase()}</h4>
                  <p>{term.comments}</p>
                  <p>Classes & Finals: {format( term.start_date, "MMM, i" )} to {format( term.end_date, "MMM, i" )}</p>
                </div>
              ))}
            </div>
          </div>
          <div className="text-right align-right justify-items-center">
            <Link href="/" className="text-white qasid-logo">
              <img className="mb-6" width="122px" height="47px" src="/qasid/qasid_logo_white.png" />
            </Link>
          </div>
        </div>

        <Separator className="my-6" />

        <div className="text-center">
          <p className="mt-2 text-sm text-gray-300">
            ©{new Date().getFullYear()} Qasid Arabic Institute. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
